{% extends "base.html" %}

{% block title %}Mes Offres - SABTRANS{% endblock %}

{# Initialisation sécurisée des variables #}
{% set filters = filters or {} %}
{% set total_offers = total_offers or 0 %}
{% set active_offers = active_offers or 0 %}
{% set assigned_offers = assigned_offers or 0 %}
{% set completed_offers = completed_offers or 0 %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary">
                        <i class="fas fa-list me-2"></i>Mes Offres de Fret
                    </h1>
                    <p class="text-muted">Gérez vos offres publiées</p>
                </div>
                <div>
                    <a href="{{ url_for('freight.create') }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Nouvelle Offre
                    </a>
                    <a href="{{ url_for('freight.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-search me-2"></i>Parcourir les Offres
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Total Offres</h6>
                            <h2 class="stat-number">{{ total_offers }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clipboard-list fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card success h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Offres Actives</h6>
                            <h2 class="stat-number text-success">{{ active_offers }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-bullhorn fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card warning h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Offres Assignées</h6>
                            <h2 class="stat-number text-warning">{{ assigned_offers }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-handshake fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card info h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Offres Terminées</h6>
                            <h2 class="stat-number text-info">{{ completed_offers }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>Filtres
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('freight.my_offers') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="status" class="form-label">Statut</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">Tous les statuts</option>
                            <option value="active" {% if filters.get('status') == 'active' %}selected{% endif %}>Active</option>
                            <option value="assigned" {% if filters.get('status') == 'assigned' %}selected{% endif %}>Assignée</option>
                            <option value="completed" {% if filters.get('status') == 'completed' %}selected{% endif %}>Terminée</option>
                            <option value="cancelled" {% if filters.get('status') == 'cancelled' %}selected{% endif %}>Annulée</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="offer_type" class="form-label">Type d'offre</label>
                        <select class="form-select" id="offer_type" name="offer_type">
                            <option value="">Tous les types</option>
                            <option value="demande" {% if filters.get('offer_type') == 'demande' %}selected{% endif %}>Demande</option>
                            <option value="offre" {% if filters.get('offer_type') == 'offre' %}selected{% endif %}>Offre</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="date_from" class="form-label">Date de création</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" 
                               value="{{ filters.get('date_from', '') }}">
                    </div>
                    <div class="col-md-3 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Filtrer
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des offres -->
    <div class="row">
        <div class="col-12">
            {% if offers.items %}
                <!-- Options d'affichage -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <p class="text-muted mb-0">
                        {{ offers.total }} offre(s) - Page {{ offers.page }} sur {{ offers.pages }}
                    </p>
                    <div class="btn-group btn-group-sm" role="group">
                        <input type="radio" class="btn-check" name="view" id="view-list" checked>
                        <label class="btn btn-outline-primary" for="view-list">
                            <i class="fas fa-list"></i>
                        </label>
                        <input type="radio" class="btn-check" name="view" id="view-grid">
                        <label class="btn btn-outline-primary" for="view-grid">
                            <i class="fas fa-th"></i>
                        </label>
                    </div>
                </div>

                <!-- Liste des offres -->
                <div id="offers-list">
                    {% for offer in offers.items %}
                    <div class="card freight-card mb-3 {% if offer.is_urgent %}urgent{% endif %}">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h5 class="card-title mb-1">
                                            {{ offer.title }}
                                            {% if offer.is_urgent %}
                                                <span class="badge bg-danger ms-2">URGENT</span>
                                            {% endif %}
                                            {% if offer.is_private %}
                                                <span class="badge bg-warning ms-2">PRIVÉ</span>
                                            {% endif %}
                                        </h5>
                                        <small class="text-muted">
                                            {{ offer.created_at.strftime('%d/%m/%Y %H:%M') }}
                                        </small>
                                    </div>
                                    
                                    <div class="row mb-2">
                                        <div class="col-md-6">
                                            <p class="mb-1">
                                                <i class="fas fa-box text-primary me-2"></i>
                                                <strong>{{ offer.goods_type }}</strong>
                                                {% if offer.weight %}
                                                    - {{ offer.weight }}t
                                                {% endif %}
                                                {% if offer.volume %}
                                                    - {{ offer.volume }}m³
                                                {% endif %}
                                            </p>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="mb-1">
                                                <i class="fas fa-calendar text-success me-2"></i>
                                                {{ offer.pickup_date.strftime('%d/%m/%Y') }}
                                                {% if offer.flexible_dates %}
                                                    <span class="badge bg-info ms-1">Flexible</span>
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <div class="route-info mb-2">
                                        <i class="fas fa-route text-warning me-2"></i>
                                        <span class="fw-bold">{{ offer.get_route_summary() }}</span>
                                        {% if offer.distance_km %}
                                            <small class="text-muted ms-2">({{ offer.distance_km }} km)</small>
                                        {% endif %}
                                    </div>
                                    
                                    {% if offer.description %}
                                    <p class="text-muted small mb-2">{{ offer.description[:100] }}...</p>
                                    {% endif %}
                                    
                                    <div class="d-flex flex-wrap gap-1">
                                        <span class="badge bg-{{ 'primary' if offer.offer_type == 'demande' else 'success' }}">
                                            {% if offer.offer_type %}
                                                {{ offer.offer_type.title() }}
                                            {% else %}
                                                Type non défini
                                            {% endif %}
                                        </span>
                                        <span class="badge bg-{{ 'success' if offer.status == 'active' else 'secondary' }}">
                                            {% if offer.status %}
                                                {{ offer.status.title() }}
                                            {% else %}
                                                Statut non défini
                                            {% endif %}
                                        </span>
                                        {% if offer.vehicle_type %}
                                            <span class="badge bg-info">{{ offer.vehicle_type }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="col-md-4 text-end">
                                    {% if offer.price %}
                                    <div class="price-info mb-3">
                                        <h4 class="text-success mb-0">{{ "%.0f"|format(offer.price) }}€</h4>
                                        <small class="text-muted">{{ offer.price_type }}</small>
                                    </div>
                                    {% endif %}
                                    
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('freight.detail', offer_id=offer.id) }}" 
                                           class="btn btn-primary">
                                            <i class="fas fa-eye me-2"></i>Voir détails
                                        </a>
                                        
                                        {% if offer.status == 'active' %}
                                        <a href="{{ url_for('freight.edit', offer_id=offer.id) }}" 
                                           class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-edit me-2"></i>Modifier
                                        </a>
                                        <button class="btn btn-outline-danger btn-sm" 
                                                onclick="cancelOffer({{ offer.id }})">
                                            <i class="fas fa-times me-2"></i>Annuler
                                        </button>
                                        {% endif %}
                                        
                                        {% if offer.status == 'assigned' %}
                                        <a href="{{ url_for('freight.mission_detail', offer_id=offer.id) }}" 
                                           class="btn btn-outline-info btn-sm">
                                            <i class="fas fa-tasks me-2"></i>Voir mission
                                        </a>
                                        {% endif %}
                                    </div>
                                    
                                    <small class="text-muted d-block mt-2">
                                        {% if offer.expires_at %}
                                            Expire le {{ offer.expires_at.strftime('%d/%m/%Y') }}
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if offers.pages > 1 %}
                <nav aria-label="Navigation des offres">
                    <ul class="pagination justify-content-center">
                        {% if offers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('freight.my_offers', page=offers.prev_num, status=filters.get('status', ''), offer_type=filters.get('offer_type', ''), date_from=filters.get('date_from', '')) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in offers.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != offers.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('freight.my_offers', page=page_num, status=filters.get('status', ''), offer_type=filters.get('offer_type', ''), date_from=filters.get('date_from', '')) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if offers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('freight.my_offers', page=offers.next_num, status=filters.get('status', ''), offer_type=filters.get('offer_type', ''), date_from=filters.get('date_from', '')) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <!-- Aucune offre -->
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Aucune offre trouvée</h4>
                    <p class="text-muted">
                        {% if filters.get('status') or filters.get('offer_type') or filters.get('date_from') %}
                            Aucune offre ne correspond à vos critères de recherche.
                        {% else %}
                            Vous n'avez pas encore publié d'offres.
                        {% endif %}
                    </p>
                    <a href="{{ url_for('freight.create') }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Créer ma première offre
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function cancelOffer(offerId) {
    if (confirm('Êtes-vous sûr de vouloir annuler cette offre ? Cette action est irréversible.')) {
        // Créer un formulaire pour l'annulation
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/freight/${offerId}/cancel`;
        
        // Ajouter un token CSRF si nécessaire
        const csrfToken = document.querySelector('meta[name=csrf-token]');
        if (csrfToken) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'csrf_token';
            input.value = csrfToken.getAttribute('content');
            form.appendChild(input);
        }
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Changement de vue
document.addEventListener('DOMContentLoaded', function() {
    const gridView = document.getElementById('view-grid');
    const listView = document.getElementById('view-list');
    const offersContainer = document.getElementById('offers-list');
    
    listView.addEventListener('change', function() {
        if (this.checked) {
            offersContainer.className = '';
            // Vue liste (par défaut)
        }
    });
    
    gridView.addEventListener('change', function() {
        if (this.checked) {
            offersContainer.className = 'row';
            // Transformer en grille
            const cards = offersContainer.querySelectorAll('.freight-card');
            cards.forEach(card => {
                card.parentElement.className = 'col-lg-6 mb-3';
            });
        }
    });
});

// Auto-submit des filtres
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const selects = form.querySelectorAll('select, input[type="date"]');
    
    selects.forEach(select => {
        select.addEventListener('change', function() {
            // Auto-submit après un délai
            setTimeout(() => {
                form.submit();
            }, 300);
        });
    });
});
</script>
{% endblock %}

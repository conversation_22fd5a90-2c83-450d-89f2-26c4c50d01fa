from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.freight import FreightOffer, FreightProposal
from models.mission import Mission
from models.user import User
from models.notification import Notification
from app import db
from datetime import datetime, timedelta
from sqlalchemy import or_, and_, desc

freight_bp = Blueprint('freight', __name__)

@freight_bp.route('/')
@login_required
def index():
    """Page principale de la bourse de fret"""
    # Récupération des paramètres de recherche
    search_query = request.args.get('search', '')
    goods_type = request.args.get('goods_type', '')
    pickup_city = request.args.get('pickup_city', '')
    delivery_city = request.args.get('delivery_city', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    offer_type = request.args.get('offer_type', '')
    page = request.args.get('page', 1, type=int)
    
    # Construction de la requête
    query = FreightOffer.query.filter_by(status='active')
    
    # Filtres selon le type d'utilisateur - NOUVEAU SYSTÈME
    if current_user.is_transporter():
        # Les transporteurs voient TOUTES les demandes actives des expéditeurs
        query = query.filter(FreightOffer.offer_type == 'demande')
        query = query.filter(FreightOffer.user_id != current_user.id)
    elif current_user.is_shipper():
        # Les expéditeurs voient LEURS PROPRES demandes avec les propositions reçues
        query = query.filter(FreightOffer.user_id == current_user.id)
        query = query.filter(FreightOffer.offer_type == 'demande')
    else:
        # Admins et autres voient toutes les demandes actives
        query = query.filter(FreightOffer.offer_type == 'demande')
    
    # Application des filtres de recherche
    if search_query:
        query = query.filter(
            or_(
                FreightOffer.title.contains(search_query),
                FreightOffer.description.contains(search_query),
                FreightOffer.goods_type.contains(search_query)
            )
        )
    
    if goods_type:
        query = query.filter(FreightOffer.goods_type.contains(goods_type))
    
    if pickup_city:
        query = query.filter(FreightOffer.pickup_city.contains(pickup_city))
    
    if delivery_city:
        query = query.filter(FreightOffer.delivery_city.contains(delivery_city))
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(FreightOffer.pickup_date >= date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(FreightOffer.pickup_date <= date_to_obj)
        except ValueError:
            pass
    
    if offer_type:
        query = query.filter(FreightOffer.offer_type == offer_type)
    
    # Paramètres de tri
    sort_by = request.args.get('sort_by', 'created_at')
    order = request.args.get('order', 'desc')

    # Tri spécial pour les demandes : afficher d'abord celles avec le meilleur prix proposé
    if offer_type == 'demande' and sort_by == 'best_price':
        # Sous-requête pour obtenir le meilleur prix par offre
        from sqlalchemy import func
        subquery = db.session.query(
            FreightProposal.freight_offer_id,
            func.min(FreightProposal.proposed_price).label('best_price')
        ).filter(FreightProposal.status == 'pending')\
         .group_by(FreightProposal.freight_offer_id).subquery()

        query = query.outerjoin(subquery, FreightOffer.id == subquery.c.freight_offer_id)
        if order == 'desc':
            query = query.order_by(desc(subquery.c.best_price), desc(FreightOffer.created_at))
        else:
            query = query.order_by(subquery.c.best_price.asc(), FreightOffer.created_at.desc())

    # Tri normal
    elif sort_by == 'price':
        if order == 'desc':
            query = query.order_by(desc(FreightOffer.price))
        else:
            query = query.order_by(FreightOffer.price.asc())
    elif sort_by == 'distance':
        if order == 'desc':
            query = query.order_by(desc(FreightOffer.distance_km))
        else:
            query = query.order_by(FreightOffer.distance_km.asc())
    elif sort_by == 'pickup_date':
        if order == 'desc':
            query = query.order_by(desc(FreightOffer.pickup_date))
        else:
            query = query.order_by(FreightOffer.pickup_date.asc())
    else:  # created_at par défaut
        if order == 'desc':
            query = query.order_by(desc(FreightOffer.created_at))
        else:
            query = query.order_by(FreightOffer.created_at.asc())
    
    # Pagination
    offers = query.paginate(
        page=page, per_page=10, error_out=False
    )
    
    # Types de marchandises pour le filtre
    goods_types = db.session.query(FreightOffer.goods_type).distinct().all()
    goods_types = [g[0] for g in goods_types if g[0]]
    
    return render_template('freight/index.html',
                         offers=offers,
                         goods_types=goods_types,
                         search_query=search_query,
                         filters={
                             'goods_type': goods_type,
                             'pickup_city': pickup_city,
                             'delivery_city': delivery_city,
                             'date_from': date_from,
                             'date_to': date_to,
                             'offer_type': offer_type,
                             'sort_by': sort_by,
                             'order': order
                         })

@freight_bp.route('/my-offers')
@login_required
def my_offers():
    """Mes offres publiées"""
    page = request.args.get('page', 1, type=int)

    # Filtres avec valeurs par défaut
    status_filter = request.args.get('status', '') or ''
    offer_type_filter = request.args.get('offer_type', '') or ''
    date_from = request.args.get('date_from', '') or ''

    # Construction de la requête
    query = FreightOffer.query.filter_by(user_id=current_user.id)

    # Application des filtres
    if status_filter:
        query = query.filter(FreightOffer.status == status_filter)

    if offer_type_filter:
        query = query.filter(FreightOffer.offer_type == offer_type_filter)

    if date_from:
        from datetime import datetime
        try:
            date_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(FreightOffer.created_at >= date_obj)
        except ValueError:
            pass  # Ignorer les dates invalides

    # Tri et pagination
    offers = query.order_by(desc(FreightOffer.created_at)).paginate(
        page=page, per_page=10, error_out=False
    )

    # Statistiques pour le template
    total_offers = FreightOffer.query.filter_by(user_id=current_user.id).count()
    active_offers = FreightOffer.query.filter_by(user_id=current_user.id, status='active').count()
    assigned_offers = FreightOffer.query.filter_by(user_id=current_user.id, status='assigned').count()
    completed_offers = FreightOffer.query.filter_by(user_id=current_user.id, status='completed').count()

    # Filtres pour le template - TOUJOURS défini
    filters = {
        'status': status_filter,
        'offer_type': offer_type_filter,
        'date_from': date_from
    }

    return render_template('freight/my_offers.html',
                         offers=offers,
                         filters=filters,
                         total_offers=total_offers,
                         active_offers=active_offers,
                         assigned_offers=assigned_offers,
                         completed_offers=completed_offers)

@freight_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """Créer une nouvelle offre de fret"""
    if request.method == 'POST':
        # Récupération des données du formulaire
        title = request.form.get('title', '').strip()
        description = request.form.get('description', '').strip()
        offer_type = request.form.get('offer_type', '')
        goods_type = request.form.get('goods_type', '').strip()
        goods_description = request.form.get('goods_description', '').strip()
        weight = request.form.get('weight', type=float)
        volume = request.form.get('volume', type=float)
        quantity = request.form.get('quantity', type=int)
        packaging = request.form.get('packaging', '').strip()
        
        # Lieux
        pickup_address = request.form.get('pickup_address', '').strip()
        pickup_city = request.form.get('pickup_city', '').strip()
        pickup_postal_code = request.form.get('pickup_postal_code', '').strip()
        delivery_address = request.form.get('delivery_address', '').strip()
        delivery_city = request.form.get('delivery_city', '').strip()
        delivery_postal_code = request.form.get('delivery_postal_code', '').strip()
        
        # Dates
        pickup_date_str = request.form.get('pickup_date', '')
        delivery_date_str = request.form.get('delivery_date', '')
        flexible_dates = bool(request.form.get('flexible_dates'))
        
        # Véhicule
        vehicle_type = request.form.get('vehicle_type', '').strip()
        vehicle_length = request.form.get('vehicle_length', type=float)
        special_requirements = request.form.get('special_requirements', '').strip()
        
        # Prix
        price = request.form.get('price', type=float)
        price_type = request.form.get('price_type', 'negociable')
        
        # Options
        is_urgent = bool(request.form.get('is_urgent'))
        is_private = bool(request.form.get('is_private'))
        requires_insurance = bool(request.form.get('requires_insurance'))
        
        # Validation des données
        errors = []
        
        if not all([title, offer_type, goods_type, pickup_address, pickup_city, delivery_address, delivery_city, pickup_date_str]):
            errors.append('Veuillez remplir tous les champs obligatoires')
        
        if offer_type not in ['demande', 'offre']:
            errors.append('Type d\'offre invalide')
        
        # Validation des dates
        try:
            pickup_date = datetime.strptime(pickup_date_str, '%Y-%m-%d')
            if pickup_date.date() < datetime.utcnow().date():
                errors.append('La date de collecte ne peut pas être dans le passé')
        except ValueError:
            errors.append('Format de date de collecte invalide')
            pickup_date = None
        
        delivery_date = None
        if delivery_date_str:
            try:
                delivery_date = datetime.strptime(delivery_date_str, '%Y-%m-%d')
                if pickup_date and delivery_date < pickup_date:
                    errors.append('La date de livraison doit être postérieure à la date de collecte')
            except ValueError:
                errors.append('Format de date de livraison invalide')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('freight/create.html')
        
        # Création de l'offre
        try:
            offer = FreightOffer(
                user_id=current_user.id,
                title=title,
                description=description,
                offer_type=offer_type,
                goods_type=goods_type,
                goods_description=goods_description,
                weight=weight,
                volume=volume,
                quantity=quantity,
                packaging=packaging,
                pickup_address=pickup_address,
                pickup_city=pickup_city,
                pickup_postal_code=pickup_postal_code,
                delivery_address=delivery_address,
                delivery_city=delivery_city,
                delivery_postal_code=delivery_postal_code,
                pickup_date=pickup_date,
                delivery_date=delivery_date,
                flexible_dates=flexible_dates,
                vehicle_type=vehicle_type,
                vehicle_length=vehicle_length,
                special_requirements=special_requirements,
                price=price,
                price_type=price_type,
                is_urgent=is_urgent,
                is_private=is_private,
                requires_insurance=requires_insurance,
                expires_at=datetime.utcnow() + timedelta(days=30)  # Expire dans 30 jours
            )
            
            # Calcul de la distance (simplifié)
            offer.calculate_distance()
            
            db.session.add(offer)
            db.session.commit()
            
            flash('Offre créée avec succès!', 'success')
            return redirect(url_for('freight.detail', offer_id=offer.id))
            
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la création de l\'offre', 'error')
    
    return render_template('freight/create.html')

@freight_bp.route('/<int:offer_id>')
@login_required
def detail(offer_id):
    """Détail d'une offre de fret"""
    offer = FreightOffer.query.get_or_404(offer_id)
    
    # Vérifier si l'utilisateur peut voir cette offre
    if offer.is_private and offer.user_id != current_user.id:
        flash('Cette offre n\'est pas accessible', 'error')
        return redirect(url_for('freight.index'))
    
    # Vérifier s'il y a déjà une mission pour cette offre
    existing_mission = Mission.query.filter_by(freight_offer_id=offer.id).first()

    # Récupérer les propositions pour cette offre (si c'est une demande)
    proposals = []
    if offer.offer_type == 'demande':
        proposals = FreightProposal.query.filter_by(freight_offer_id=offer_id)\
                                        .order_by(FreightProposal.proposed_price.asc())\
                                        .all()

    return render_template('freight/detail.html', offer=offer, existing_mission=existing_mission, proposals=proposals)

@freight_bp.route('/<int:offer_id>/accept', methods=['POST'])
@login_required
def accept_offer(offer_id):
    """Accepter une offre de fret"""
    offer = FreightOffer.query.get_or_404(offer_id)
    
    # Vérifications
    if offer.user_id == current_user.id:
        flash('Vous ne pouvez pas accepter votre propre offre', 'error')
        return redirect(url_for('freight.detail', offer_id=offer_id))
    
    if offer.status != 'active':
        flash('Cette offre n\'est plus disponible', 'error')
        return redirect(url_for('freight.detail', offer_id=offer_id))
    
    # Vérifier s'il y a déjà une mission
    existing_mission = Mission.query.filter_by(freight_offer_id=offer.id).first()
    if existing_mission:
        flash('Cette offre a déjà été acceptée', 'error')
        return redirect(url_for('freight.detail', offer_id=offer_id))
    
    # Vérifier le type d'utilisateur
    if offer.offer_type == 'demande' and not current_user.is_transporter():
        flash('Seuls les transporteurs peuvent accepter les demandes', 'error')
        return redirect(url_for('freight.detail', offer_id=offer_id))
    
    if offer.offer_type == 'offre' and not current_user.is_shipper():
        flash('Seuls les expéditeurs peuvent accepter les offres', 'error')
        return redirect(url_for('freight.detail', offer_id=offer_id))
    
    # Création de la mission
    try:
        mission = Mission(
            freight_offer_id=offer.id,
            transporter_id=current_user.id if offer.offer_type == 'demande' else offer.user_id,
            shipper_id=offer.user_id if offer.offer_type == 'demande' else current_user.id,
            title=offer.title,
            description=f"Mission créée à partir de l'offre: {offer.title}",
            pickup_scheduled_at=offer.pickup_date,
            delivery_scheduled_at=offer.delivery_date,
            agreed_price=offer.price,
            status='pending'
        )
        
        mission.generate_mission_number()
        
        # Mettre à jour le statut de l'offre
        offer.status = 'assigned'
        
        db.session.add(mission)
        db.session.commit()
        
        flash('Offre acceptée! Une mission a été créée.', 'success')
        return redirect(url_for('dashboard.index'))
        
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de l\'acceptation de l\'offre', 'error')
        return redirect(url_for('freight.detail', offer_id=offer_id))

@freight_bp.route('/<int:offer_id>/edit', methods=['GET', 'POST'])
@login_required
def edit(offer_id):
    """Modifier une offre de fret"""
    offer = FreightOffer.query.get_or_404(offer_id)
    
    # Vérifier que l'utilisateur est le propriétaire
    if offer.user_id != current_user.id:
        flash('Vous ne pouvez modifier que vos propres offres', 'error')
        return redirect(url_for('freight.detail', offer_id=offer_id))
    
    # Vérifier que l'offre peut être modifiée
    if offer.status != 'active':
        flash('Cette offre ne peut plus être modifiée', 'error')
        return redirect(url_for('freight.detail', offer_id=offer_id))
    
    if request.method == 'POST':
        # Mise à jour des champs (logique similaire à create)
        offer.title = request.form.get('title', '').strip()
        offer.description = request.form.get('description', '').strip()
        offer.goods_type = request.form.get('goods_type', '').strip()
        offer.price = request.form.get('price', type=float)
        # ... autres champs
        
        try:
            db.session.commit()
            flash('Offre mise à jour avec succès!', 'success')
            return redirect(url_for('freight.detail', offer_id=offer.id))
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la mise à jour', 'error')
    
    return render_template('freight/edit.html', offer=offer)

@freight_bp.route('/<int:offer_id>/delete', methods=['POST'])
@login_required
def delete(offer_id):
    """Supprimer une offre de fret"""
    offer = FreightOffer.query.get_or_404(offer_id)
    
    # Vérifier que l'utilisateur est le propriétaire
    if offer.user_id != current_user.id:
        flash('Vous ne pouvez supprimer que vos propres offres', 'error')
        return redirect(url_for('freight.detail', offer_id=offer_id))
    
    # Vérifier qu'il n'y a pas de mission associée
    existing_mission = Mission.query.filter_by(freight_offer_id=offer.id).first()
    if existing_mission:
        flash('Impossible de supprimer une offre avec une mission associée', 'error')
        return redirect(url_for('freight.detail', offer_id=offer_id))
    
    try:
        db.session.delete(offer)
        db.session.commit()
        flash('Offre supprimée avec succès', 'success')
        return redirect(url_for('freight.my_offers'))
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de la suppression', 'error')
        return redirect(url_for('freight.detail', offer_id=offer_id))

@freight_bp.route('/<int:offer_id>/propose', methods=['POST'])
@login_required
def propose_price(offer_id):
    """Proposer un prix pour une demande de fret"""
    offer = FreightOffer.query.get_or_404(offer_id)

    # Vérifications
    if offer.offer_type != 'demande':
        return jsonify({'success': False, 'message': 'Seules les demandes acceptent des propositions'})

    if offer.user_id == current_user.id:
        return jsonify({'success': False, 'message': 'Vous ne pouvez pas proposer un prix pour votre propre demande'})

    if offer.status != 'active':
        return jsonify({'success': False, 'message': 'Cette demande n\'est plus active'})

    if current_user.user_type != 'transporteur':
        return jsonify({'success': False, 'message': 'Seuls les transporteurs peuvent proposer des prix'})

    # Vérifier si l'utilisateur a déjà fait une proposition
    existing_proposal = FreightProposal.query.filter_by(
        freight_offer_id=offer_id,
        transporter_id=current_user.id
    ).first()

    if existing_proposal:
        return jsonify({'success': False, 'message': 'Vous avez déjà fait une proposition pour cette demande'})

    # Récupérer les données du formulaire - VERSION ÉTENDUE
    try:
        proposed_price = float(request.form.get('proposed_price', 0))
        price_type = request.form.get('price_type', 'fixe')
        message = request.form.get('message', '').strip()
        estimated_duration = request.form.get('estimated_duration', type=int)
        vehicle_details = request.form.get('vehicle_details', '').strip()

        # Nouveaux champs détaillés
        vehicle_type = request.form.get('vehicle_type', '').strip()
        vehicle_capacity = request.form.get('vehicle_capacity', type=float)
        vehicle_volume = request.form.get('vehicle_volume', type=float)
        vehicle_registration = request.form.get('vehicle_registration', '').strip()
        pickup_flexibility = 'pickup_flexibility' in request.form
        delivery_flexibility = 'delivery_flexibility' in request.form
        insurance_coverage = request.form.get('insurance_coverage', type=float)
        additional_services = request.form.get('additional_services', '').strip()
        similar_transports_count = request.form.get('similar_transports_count', type=int) or 0
        client_references = request.form.get('client_references', '').strip()

        if proposed_price <= 0:
            return jsonify({'success': False, 'message': 'Le prix proposé doit être supérieur à 0'})

        # Créer la proposition avec tous les détails
        proposal = FreightProposal(
            freight_offer_id=offer_id,
            transporter_id=current_user.id,
            proposed_price=proposed_price,
            price_type=price_type,
            message=message,
            estimated_duration=estimated_duration,
            vehicle_details=vehicle_details,
            vehicle_type=vehicle_type,
            vehicle_capacity=vehicle_capacity,
            vehicle_volume=vehicle_volume,
            vehicle_registration=vehicle_registration,
            pickup_flexibility=pickup_flexibility,
            delivery_flexibility=delivery_flexibility,
            insurance_coverage=insurance_coverage,
            additional_services=additional_services,
            similar_transports_count=similar_transports_count,
            client_references=client_references
        )

        db.session.add(proposal)

        # Mettre à jour le compteur de propositions
        offer.proposals_count = FreightProposal.query.filter_by(freight_offer_id=offer_id).count() + 1

        # Créer une notification pour l'expéditeur
        Notification.create_notification(
            user_id=offer.user_id,
            notification_type='new_proposal',
            title='Nouvelle proposition reçue',
            message=f'{current_user.get_full_name()} a proposé {proposed_price}€ pour votre demande "{offer.title}".',
            freight_offer_id=offer_id,
            freight_proposal_id=proposal.id
        )

        db.session.commit()

        flash('Votre proposition a été envoyée avec succès!', 'success')
        return jsonify({'success': True, 'message': 'Proposition envoyée avec succès'})

    except ValueError:
        return jsonify({'success': False, 'message': 'Prix invalide'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Erreur lors de l\'envoi de la proposition'})

@freight_bp.route('/proposal/<int:proposal_id>/accept', methods=['POST'])
@login_required
def accept_proposal(proposal_id):
    """Accepter une proposition de prix"""
    proposal = FreightProposal.query.get_or_404(proposal_id)
    offer = proposal.freight_offer

    # Vérifications
    if offer.user_id != current_user.id:
        return jsonify({'success': False, 'message': 'Seul l\'expéditeur peut accepter les propositions'})

    if proposal.status != 'pending':
        return jsonify({'success': False, 'message': 'Cette proposition n\'est plus disponible'})

    if offer.status != 'active':
        return jsonify({'success': False, 'message': 'Cette demande n\'est plus active'})

    try:
        # Accepter la proposition
        proposal.status = 'accepted'
        proposal.responded_at = datetime.utcnow()

        # Rejeter toutes les autres propositions
        other_proposals = FreightProposal.query.filter(
            FreightProposal.freight_offer_id == offer.id,
            FreightProposal.id != proposal_id,
            FreightProposal.status == 'pending'
        ).all()

        for other_proposal in other_proposals:
            other_proposal.status = 'rejected'
            other_proposal.responded_at = datetime.utcnow()

        # Créer une mission avec le prix accepté
        mission = Mission(
            freight_offer_id=offer.id,
            transporter_id=proposal.transporter_id,
            shipper_id=offer.user_id,
            title=offer.title,
            description=f"Mission créée à partir de la proposition acceptée: {proposal.proposed_price}€",
            pickup_scheduled_at=offer.pickup_date,
            delivery_scheduled_at=offer.delivery_date,
            agreed_price=proposal.proposed_price,
            status='pending'
        )

        mission.generate_mission_number()

        # Mettre à jour le statut de l'offre
        offer.status = 'assigned'

        db.session.add(mission)

        # Créer les notifications
        # Notification pour le transporteur accepté
        Notification.create_notification(
            user_id=proposal.transporter_id,
            notification_type='proposal_accepted',
            title='Proposition acceptée !',
            message=f'Votre proposition de {proposal.proposed_price}€ pour "{offer.title}" a été acceptée. Une mission a été créée.',
            freight_offer_id=offer.id,
            freight_proposal_id=proposal.id,
            mission_id=mission.id
        )

        # Notifications pour les transporteurs rejetés
        for other_proposal in other_proposals:
            Notification.create_notification(
                user_id=other_proposal.transporter_id,
                notification_type='proposal_rejected',
                title='Proposition non retenue',
                message=f'Votre proposition pour "{offer.title}" n\'a pas été retenue. L\'expéditeur a choisi une autre offre.',
                freight_offer_id=offer.id,
                freight_proposal_id=other_proposal.id
            )

        db.session.commit()

        flash(f'Proposition acceptée! Mission créée avec {proposal.transporter.get_full_name()} pour {proposal.proposed_price}€', 'success')
        return jsonify({'success': True, 'message': 'Proposition acceptée et mission créée'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Erreur lors de l\'acceptation de la proposition'})

@freight_bp.route('/proposal/<int:proposal_id>/reject', methods=['POST'])
@login_required
def reject_proposal(proposal_id):
    """Rejeter une proposition de prix"""
    proposal = FreightProposal.query.get_or_404(proposal_id)
    offer = proposal.freight_offer

    # Vérifications
    if offer.user_id != current_user.id:
        return jsonify({'success': False, 'message': 'Seul l\'expéditeur peut rejeter les propositions'})

    if proposal.status != 'pending':
        return jsonify({'success': False, 'message': 'Cette proposition n\'est plus disponible'})

    try:
        proposal.status = 'rejected'
        proposal.responded_at = datetime.utcnow()

        db.session.commit()

        flash('Proposition rejetée', 'info')
        return jsonify({'success': True, 'message': 'Proposition rejetée'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': 'Erreur lors du rejet de la proposition'})

@freight_bp.route('/notifications')
@login_required
def notifications():
    """Afficher les notifications de l'utilisateur"""
    page = request.args.get('page', 1, type=int)

    notifications = Notification.query.filter_by(user_id=current_user.id)\
                                    .filter_by(is_archived=False)\
                                    .order_by(Notification.created_at.desc())\
                                    .paginate(page=page, per_page=20, error_out=False)

    # Marquer les notifications non lues comme lues
    unread_notifications = Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).all()

    for notification in unread_notifications:
        notification.mark_as_read()

    return render_template('freight/notifications.html', notifications=notifications)

@freight_bp.route('/notifications/<int:notification_id>/archive', methods=['POST'])
@login_required
def archive_notification(notification_id):
    """Archiver une notification"""
    notification = Notification.query.get_or_404(notification_id)

    if notification.user_id != current_user.id:
        return jsonify({'success': False, 'message': 'Accès refusé'})

    notification.archive()
    return jsonify({'success': True, 'message': 'Notification archivée'})

{% extends "base.html" %}

{% block title %}Upload Document - SABTRANS{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-upload me-2"></i>Nouveau Document
                </h1>
                <a href="{{ url_for('documents.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour
                </a>
            </div>

            <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                <!-- Sélection du fichier -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-file me-2"></i>Sélection du Fichier
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="file" class="form-label">Fichier à uploader *</label>
                            <input type="file" class="form-control" id="file" name="file" required
                                   accept=".pdf,.jpg,.jpeg,.png,.gif,.doc,.docx,.xls,.xlsx,.txt">
                            <div class="form-text">
                                Formats acceptés : PDF, Images (JPG, PNG, GIF), Documents Office (DOC, XLS), Texte. 
                                Taille maximum : 16MB
                            </div>
                            <div class="invalid-feedback">Veuillez sélectionner un fichier</div>
                        </div>
                        
                        <!-- Prévisualisation -->
                        <div id="file-preview" class="mt-3"></div>
                    </div>
                </div>

                <!-- Informations du document -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Informations du Document
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="name" class="form-label">Nom du document *</label>
                                <input type="text" class="form-control" id="name" name="name" required
                                       placeholder="Ex: CMR Transport Paris-Lyon">
                                <div class="invalid-feedback">Veuillez saisir un nom pour le document</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="document_type" class="form-label">Type de document *</label>
                                <select class="form-select" id="document_type" name="document_type" required>
                                    <option value="">Sélectionnez</option>
                                    <option value="cmr">CMR (Lettre de voiture)</option>
                                    <option value="pod">Preuve de livraison (POD)</option>
                                    <option value="facture">Facture</option>
                                    <option value="bon_livraison">Bon de livraison</option>
                                    <option value="photo">Photo</option>
                                    <option value="assurance">Assurance</option>
                                    <option value="permis">Permis de conduire</option>
                                    <option value="kbis">Kbis</option>
                                    <option value="protocole">Protocole de sécurité</option>
                                    <option value="autre">Autre</option>
                                </select>
                                <div class="invalid-feedback">Veuillez sélectionner un type</div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"
                                      placeholder="Description détaillée du document..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="tags" class="form-label">Tags</label>
                            <input type="text" class="form-control" id="tags" name="tags"
                                   placeholder="transport, urgent, client-xyz (séparés par des virgules)">
                            <div class="form-text">Ajoutez des mots-clés pour faciliter la recherche</div>
                        </div>
                    </div>
                </div>

                <!-- Association à une mission -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-link me-2"></i>Association (Optionnel)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="mission_id" class="form-label">Mission associée</label>
                            <select class="form-select" id="mission_id" name="mission_id">
                                <option value="">Aucune mission</option>
                                {% for mission in missions %}
                                    <option value="{{ mission.id }}">
                                        {{ mission.mission_number }} - {{ mission.title }}
                                    </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">Associez ce document à une mission spécifique</div>
                        </div>
                    </div>
                </div>

                <!-- Options de partage -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-share-alt me-2"></i>Options de Partage
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_public" name="is_public">
                            <label class="form-check-label" for="is_public">
                                <strong>Document public</strong>
                            </label>
                            <div class="form-text">
                                Si coché, ce document sera visible par tous les participants de la mission associée
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-4">
                    <a href="{{ url_for('documents.index') }}" class="btn btn-outline-secondary me-md-2">
                        <i class="fas fa-times me-2"></i>Annuler
                    </a>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload me-2"></i>Uploader le document
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Prévisualisation du fichier
document.getElementById('file').addEventListener('change', function() {
    previewFile(this);
    
    // Auto-remplir le nom si vide
    const nameInput = document.getElementById('name');
    if (!nameInput.value && this.files[0]) {
        const fileName = this.files[0].name;
        const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
        nameInput.value = nameWithoutExt;
    }
});

// Suggestion de type de document basée sur l'extension
document.getElementById('file').addEventListener('change', function() {
    if (this.files[0]) {
        const fileName = this.files[0].name.toLowerCase();
        const typeSelect = document.getElementById('document_type');
        
        if (fileName.includes('cmr')) {
            typeSelect.value = 'cmr';
        } else if (fileName.includes('pod') || fileName.includes('livraison')) {
            typeSelect.value = 'pod';
        } else if (fileName.includes('facture') || fileName.includes('invoice')) {
            typeSelect.value = 'facture';
        } else if (fileName.includes('photo') || fileName.match(/\.(jpg|jpeg|png|gif)$/)) {
            typeSelect.value = 'photo';
        } else if (fileName.includes('assurance')) {
            typeSelect.value = 'assurance';
        } else if (fileName.includes('permis')) {
            typeSelect.value = 'permis';
        } else if (fileName.includes('kbis')) {
            typeSelect.value = 'kbis';
        } else if (fileName.includes('protocole')) {
            typeSelect.value = 'protocole';
        }
    }
});

// Validation de la taille du fichier
document.getElementById('file').addEventListener('change', function() {
    const maxSize = 16 * 1024 * 1024; // 16MB
    
    if (this.files[0] && this.files[0].size > maxSize) {
        alert('Le fichier est trop volumineux. Taille maximum autorisée : 16MB');
        this.value = '';
        document.getElementById('file-preview').innerHTML = '';
    }
});

// Gestion du drag & drop
const fileInput = document.getElementById('file');
const cardBody = fileInput.closest('.card-body');

['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
    cardBody.addEventListener(eventName, preventDefaults, false);
});

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

['dragenter', 'dragover'].forEach(eventName => {
    cardBody.addEventListener(eventName, highlight, false);
});

['dragleave', 'drop'].forEach(eventName => {
    cardBody.addEventListener(eventName, unhighlight, false);
});

function highlight(e) {
    cardBody.classList.add('border-primary', 'bg-light');
}

function unhighlight(e) {
    cardBody.classList.remove('border-primary', 'bg-light');
}

cardBody.addEventListener('drop', handleDrop, false);

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;
    
    if (files.length > 0) {
        fileInput.files = files;
        previewFile(fileInput);
    }
}
</script>
{% endblock %}

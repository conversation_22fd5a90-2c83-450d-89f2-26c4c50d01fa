from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from functools import wraps
from werkzeug.security import generate_password_hash
from models.user import User
from models.freight import FreightOffer, FreightProposal
from models.mission import Mission
from models.document import Document
from database import db
import secrets
import string
from datetime import datetime

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

def admin_required(f):
    """Décorateur pour vérifier que l'utilisateur est administrateur"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.user_type != 'admin':
            flash('Accès refusé. Droits administrateur requis.', 'error')
            return redirect(url_for('dashboard.index'))
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/users')
@login_required
@admin_required
def users():
    """Liste des utilisateurs avec filtres et pagination"""
    # Initialisation des valeurs par défaut sécurisées
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # Filtres avec valeurs par défaut garanties
    search_query = request.args.get('search', '') or ''
    user_type_filter = request.args.get('user_type', '') or ''
    status_filter = request.args.get('status', '') or ''
    sort_by = request.args.get('sort_by', 'created_at') or 'created_at'
    order = request.args.get('order', 'desc') or 'desc'

    # Filtres pour le template - TOUJOURS défini
    filters = {
        'user_type': user_type_filter,
        'status': status_filter,
        'sort_by': sort_by,
        'order': order,
        'search': search_query
    }

    try:
        # Construction de la requête
        query = User.query

        # Recherche textuelle
        if search_query:
            search_pattern = f'%{search_query}%'
            query = query.filter(
                db.or_(
                    User.first_name.ilike(search_pattern),
                    User.last_name.ilike(search_pattern),
                    User.email.ilike(search_pattern),
                    User.username.ilike(search_pattern),
                    User.company_name.ilike(search_pattern)
                )
            )

        # Filtre par type d'utilisateur
        if user_type_filter:
            query = query.filter(User.user_type == user_type_filter)

        # Filtre par statut
        if status_filter == 'active':
            query = query.filter(User.is_active == True)
        elif status_filter == 'inactive':
            query = query.filter(User.is_active == False)
        elif status_filter == 'verified':
            query = query.filter(User.is_verified == True)
        elif status_filter == 'unverified':
            query = query.filter(User.is_verified == False)

        # Tri
        if hasattr(User, sort_by):
            if order == 'desc':
                query = query.order_by(getattr(User, sort_by).desc())
            else:
                query = query.order_by(getattr(User, sort_by).asc())

        # Pagination
        users = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Statistiques
        total_users = User.query.count()
        active_users = User.query.filter(User.is_active == True).count()
        pending_users = User.query.filter(User.is_verified == False).count()

        # Nouveaux utilisateurs cette semaine
        from datetime import datetime, timedelta
        week_ago = datetime.utcnow() - timedelta(days=7)
        new_users_week = User.query.filter(User.created_at >= week_ago).count()

    except Exception as e:
        # En cas d'erreur, utiliser des valeurs par défaut
        flash(f'Erreur lors du chargement des utilisateurs: {str(e)}', 'error')

        users = User.query.paginate(page=1, per_page=20, error_out=False)
        total_users = 0
        active_users = 0
        pending_users = 0
        new_users_week = 0

    # Retour TOUJOURS avec filters défini
    return render_template('admin/users.html',
                         users=users,
                         search_query=search_query,
                         filters=filters,
                         total_users=total_users,
                         active_users=active_users,
                         pending_users=pending_users,
                         new_users_week=new_users_week)

@admin_bp.route('/users-simple')
@login_required
@admin_required
def users_simple():
    """Version simplifiée de la liste des utilisateurs pour test"""
    page = request.args.get('page', 1, type=int)

    # Requête simple
    users = User.query.paginate(page=page, per_page=20, error_out=False)

    # Statistiques simples
    total_users = User.query.count()
    active_users = User.query.filter(User.is_active == True).count()
    pending_users = User.query.filter(User.is_verified == False).count()
    new_users_week = 0  # Simplifié pour éviter les erreurs

    # Variables garanties
    filters = {}
    search_query = ''

    return render_template('admin/users_simple.html',
                         users=users,
                         search_query=search_query,
                         filters=filters,
                         total_users=total_users,
                         active_users=active_users,
                         pending_users=pending_users,
                         new_users_week=new_users_week)

@admin_bp.route('/users/<int:user_id>')
@login_required
@admin_required
def user_detail(user_id):
    """Détail d'un utilisateur"""
    user = User.query.get_or_404(user_id)
    
    # Statistiques de l'utilisateur
    user_stats = {
        'total_offers': FreightOffer.query.filter_by(user_id=user_id).count(),
        'total_missions': Mission.query.filter_by(transporter_id=user_id).count(),
        'total_documents': Document.query.filter_by(user_id=user_id).count(),
        'avg_rating': None  # À implémenter si système de notation
    }
    
    # Offres récentes
    recent_offers = FreightOffer.query.filter_by(user_id=user_id)\
                                    .order_by(FreightOffer.created_at.desc())\
                                    .limit(5).all()
    
    return render_template('admin/user_detail.html',
                         user=user,
                         user_stats=user_stats,
                         recent_offers=recent_offers)

@admin_bp.route('/users/<int:user_id>/edit')
@login_required
@admin_required
def edit_user(user_id):
    """Formulaire d'édition d'un utilisateur"""
    user = User.query.get_or_404(user_id)
    return render_template('admin/edit_user.html', user=user)

@admin_bp.route('/users/<int:user_id>/edit', methods=['POST'])
@login_required
@admin_required
def update_user(user_id):
    """Mise à jour d'un utilisateur"""
    user = User.query.get_or_404(user_id)
    
    try:
        # Mise à jour des champs
        user.first_name = request.form.get('first_name')
        user.last_name = request.form.get('last_name')
        user.email = request.form.get('email')
        user.phone = request.form.get('phone')
        user.username = request.form.get('username')
        user.user_type = request.form.get('user_type')
        user.company_name = request.form.get('company_name')
        user.siret = request.form.get('siret')
        user.address = request.form.get('address')
        user.postal_code = request.form.get('postal_code')
        user.city = request.form.get('city')
        user.country = request.form.get('country')
        
        # Statuts
        user.is_active = 'is_active' in request.form
        user.is_verified = 'is_verified' in request.form
        user.email_confirmed = 'email_confirmed' in request.form
        
        # Notes admin
        user.admin_notes = request.form.get('admin_notes')
        
        db.session.commit()
        flash('Utilisateur mis à jour avec succès', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la mise à jour: {str(e)}', 'error')
    
    return redirect(url_for('admin.user_detail', user_id=user_id))

@admin_bp.route('/users/create', methods=['POST'])
@login_required
@admin_required
def create_user():
    """Création d'un nouvel utilisateur"""
    try:
        # Vérifier que l'email et le nom d'utilisateur sont uniques
        email = request.form.get('email')
        username = request.form.get('username')
        
        if User.query.filter_by(email=email).first():
            return jsonify({'success': False, 'message': 'Cet email est déjà utilisé'})
        
        if User.query.filter_by(username=username).first():
            return jsonify({'success': False, 'message': 'Ce nom d\'utilisateur est déjà utilisé'})
        
        # Créer le nouvel utilisateur
        user = User(
            first_name=request.form.get('first_name'),
            last_name=request.form.get('last_name'),
            email=email,
            username=username,
            user_type=request.form.get('user_type'),
            phone=request.form.get('phone'),
            company_name=request.form.get('company_name'),
            is_active='is_active' in request.form,
            is_verified='is_verified' in request.form,
            email_confirmed=True  # Confirmé par défaut pour les comptes créés par admin
        )
        
        # Définir le mot de passe
        password = request.form.get('password')
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'Utilisateur créé avec succès'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'Erreur lors de la création: {str(e)}'})

@admin_bp.route('/users/<int:user_id>/toggle-status', methods=['POST'])
@login_required
@admin_required
def toggle_user_status(user_id):
    """Activer/désactiver un utilisateur"""
    try:
        user = User.query.get_or_404(user_id)
        data = request.get_json()
        
        user.is_active = data.get('active', False)
        db.session.commit()
        
        return jsonify({'success': True})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@admin_bp.route('/users/<int:user_id>/verify', methods=['POST'])
@login_required
@admin_required
def verify_user(user_id):
    """Vérifier un utilisateur"""
    try:
        user = User.query.get_or_404(user_id)
        user.is_verified = True
        db.session.commit()
        
        return jsonify({'success': True})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@admin_bp.route('/users/<int:user_id>/reset-password', methods=['POST'])
@login_required
@admin_required
def reset_user_password(user_id):
    """Réinitialiser le mot de passe d'un utilisateur"""
    try:
        user = User.query.get_or_404(user_id)
        
        # Générer un mot de passe temporaire
        new_password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(12))
        user.set_password(new_password)
        
        db.session.commit()
        
        return jsonify({'success': True, 'new_password': new_password})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@admin_bp.route('/users/<int:user_id>/confirm-email', methods=['POST'])
@login_required
@admin_required
def confirm_user_email(user_id):
    """Confirmer manuellement l'email d'un utilisateur"""
    try:
        user = User.query.get_or_404(user_id)
        user.email_confirmed = True
        db.session.commit()
        
        return jsonify({'success': True})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@admin_bp.route('/users/<int:user_id>/send-message', methods=['POST'])
@login_required
@admin_required
def send_user_message(user_id):
    """Envoyer un message à un utilisateur"""
    try:
        user = User.query.get_or_404(user_id)
        data = request.get_json()
        message = data.get('message', '')
        
        # Ici on pourrait implémenter l'envoi d'email ou de notification
        # Pour l'instant, on simule juste le succès
        
        return jsonify({'success': True})
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@admin_bp.route('/users/<int:user_id>/save-notes', methods=['POST'])
@login_required
@admin_required
def save_user_notes(user_id):
    """Sauvegarder les notes administrateur"""
    try:
        user = User.query.get_or_404(user_id)
        data = request.get_json()
        
        user.admin_notes = data.get('notes', '')
        db.session.commit()
        
        return jsonify({'success': True})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)})

@admin_bp.route('/freight-proposals')
@login_required
@admin_required
def freight_proposals():
    """Gestion des propositions de fret pour l'admin"""
    page = request.args.get('page', 1, type=int)

    # Filtres
    status_filter = request.args.get('status', '') or ''
    sort_by = request.args.get('sort_by', 'created_at') or 'created_at'
    order = request.args.get('order', 'desc') or 'desc'

    # Construction de la requête
    query = FreightProposal.query.join(FreightOffer)

    # Application des filtres
    if status_filter:
        query = query.filter(FreightProposal.status == status_filter)

    # Tri
    if sort_by == 'proposed_price':
        if order == 'desc':
            query = query.order_by(FreightProposal.proposed_price.desc())
        else:
            query = query.order_by(FreightProposal.proposed_price.asc())
    elif sort_by == 'freight_title':
        if order == 'desc':
            query = query.order_by(FreightOffer.title.desc())
        else:
            query = query.order_by(FreightOffer.title.asc())
    else:  # created_at par défaut
        if order == 'desc':
            query = query.order_by(FreightProposal.created_at.desc())
        else:
            query = query.order_by(FreightProposal.created_at.asc())

    # Pagination
    proposals = query.paginate(page=page, per_page=20, error_out=False)

    # Statistiques
    total_proposals = FreightProposal.query.count()
    pending_proposals = FreightProposal.query.filter(FreightProposal.status == 'pending').count()
    accepted_proposals = FreightProposal.query.filter(FreightProposal.status == 'accepted').count()
    rejected_proposals = FreightProposal.query.filter(FreightProposal.status == 'rejected').count()

    # Demandes avec le plus de propositions
    from sqlalchemy import func
    top_demands = db.session.query(
        FreightOffer.id,
        FreightOffer.title,
        FreightOffer.pickup_city,
        FreightOffer.delivery_city,
        func.count(FreightProposal.id).label('proposal_count'),
        func.min(FreightProposal.proposed_price).label('best_price')
    ).join(FreightProposal)\
     .filter(FreightOffer.offer_type == 'demande')\
     .filter(FreightProposal.status == 'pending')\
     .group_by(FreightOffer.id)\
     .order_by(func.count(FreightProposal.id).desc())\
     .limit(5).all()

    # Filtres pour le template
    filters = {
        'status': status_filter,
        'sort_by': sort_by,
        'order': order
    }

    return render_template('admin/freight_proposals.html',
                         proposals=proposals,
                         filters=filters,
                         total_proposals=total_proposals,
                         pending_proposals=pending_proposals,
                         accepted_proposals=accepted_proposals,
                         rejected_proposals=rejected_proposals,
                         top_demands=top_demands)

@admin_bp.route('/users/add', methods=['POST'])
@login_required
@admin_required
def add_user():
    """Ajouter un nouvel utilisateur"""
    try:
        # Récupération des données du formulaire
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        email = request.form.get('email')
        password = request.form.get('password')
        user_type = request.form.get('user_type')
        phone = request.form.get('phone')
        company_name = request.form.get('company_name')
        city = request.form.get('city')

        # Validation des champs obligatoires
        if not all([first_name, last_name, email, password, user_type]):
            return jsonify({'success': False, 'message': 'Tous les champs obligatoires doivent être remplis'})

        # Vérification que l'email n'existe pas déjà
        if User.query.filter_by(email=email).first():
            return jsonify({'success': False, 'message': 'Un utilisateur avec cet email existe déjà'})

        # Validation du type d'utilisateur
        if user_type not in ['transporter', 'shipper']:
            return jsonify({'success': False, 'message': 'Type d\'utilisateur invalide'})

        # Conversion du type d'utilisateur vers les termes français utilisés dans la base
        user_type_mapping = {
            'transporter': 'transporteur',
            'shipper': 'expediteur'
        }
        db_user_type = user_type_mapping[user_type]

        # Génération automatique du nom d'utilisateur à partir de l'email
        username_base = email.split('@')[0]
        username = username_base
        counter = 1

        # Vérifier l'unicité du nom d'utilisateur et ajouter un suffixe si nécessaire
        while User.query.filter_by(username=username).first():
            username = f"{username_base}{counter}"
            counter += 1

        # Création du nouvel utilisateur
        new_user = User(
            first_name=first_name,
            last_name=last_name,
            email=email,
            username=username,
            user_type=db_user_type,
            phone=phone,
            company_name=company_name,
            city=city,
            is_verified=True,  # Les utilisateurs créés par l'admin sont automatiquement vérifiés
            created_at=datetime.utcnow()
        )
        new_user.set_password(password)

        db.session.add(new_user)
        db.session.commit()

        user_type_label = 'transporteur' if user_type == 'transporter' else 'expéditeur'
        return jsonify({
            'success': True,
            'message': f'{user_type_label.title()} "{first_name} {last_name}" créé avec succès'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'Erreur lors de la création: {str(e)}'})

@admin_bp.route('/users/<int:user_id>/delete', methods=['DELETE'])
@login_required
@admin_required
def delete_user(user_id):
    """Supprimer un utilisateur"""
    try:
        user = User.query.get_or_404(user_id)

        # Vérification que l'utilisateur n'est pas un admin
        if user.user_type == 'admin':
            return jsonify({'success': False, 'message': 'Impossible de supprimer un administrateur'})

        # Vérification que l'utilisateur ne se supprime pas lui-même
        if user.id == current_user.id:
            return jsonify({'success': False, 'message': 'Vous ne pouvez pas vous supprimer vous-même'})

        user_name = user.get_full_name()
        user_type = user.user_type

        # Suppression des propositions de l'utilisateur
        if user_type == 'transporter':
            FreightProposal.query.filter_by(transporter_id=user.id).delete()

        # Suppression des offres de l'utilisateur
        FreightOffer.query.filter_by(user_id=user.id).delete()

        # Suppression des missions associées
        Mission.query.filter(
            (Mission.shipper_id == user.id) | (Mission.transporter_id == user.id)
        ).delete()

        # Suppression de l'utilisateur
        db.session.delete(user)
        db.session.commit()

        user_type_label = 'transporteur' if user_type == 'transporter' else 'expéditeur'
        return jsonify({
            'success': True,
            'message': f'{user_type_label.title()} "{user_name}" supprimé avec succès'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'Erreur lors de la suppression: {str(e)}'})

@admin_bp.route('/offers/add', methods=['POST'])
@login_required
@admin_required
def add_offer():
    """Ajouter une nouvelle offre"""
    try:
        # Récupération des données du formulaire
        title = request.form.get('title')
        offer_type = request.form.get('offer_type')
        user_id = request.form.get('user_id')
        pickup_city = request.form.get('pickup_city')
        delivery_city = request.form.get('delivery_city')
        goods_type = request.form.get('goods_type')
        weight_kg = request.form.get('weight_kg')
        pickup_date = request.form.get('pickup_date')
        price = request.form.get('price')
        description = request.form.get('description')

        # Validation des champs obligatoires
        if not all([title, offer_type, user_id, pickup_city, delivery_city]):
            return jsonify({'success': False, 'message': 'Tous les champs obligatoires doivent être remplis'})

        # Vérification que l'utilisateur existe
        user = User.query.get(user_id)
        if not user:
            return jsonify({'success': False, 'message': 'Utilisateur introuvable'})

        # Validation du type d'offre
        if offer_type not in ['demande', 'offre']:
            return jsonify({'success': False, 'message': 'Type d\'offre invalide'})

        # Création de la nouvelle offre
        new_offer = FreightOffer(
            title=title,
            offer_type=offer_type,
            user_id=int(user_id),
            pickup_city=pickup_city,
            delivery_city=delivery_city,
            goods_type=goods_type,
            weight_kg=float(weight_kg) if weight_kg else None,
            pickup_date=datetime.strptime(pickup_date, '%Y-%m-%d').date() if pickup_date else None,
            price=float(price) if price else None,
            description=description,
            status='active',
            created_at=datetime.utcnow()
        )

        db.session.add(new_offer)
        db.session.commit()

        offer_type_label = 'demande' if offer_type == 'demande' else 'offre'
        return jsonify({
            'success': True,
            'message': f'{offer_type_label.title()} "{title}" créée avec succès'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'Erreur lors de la création: {str(e)}'})

@admin_bp.route('/offers/<int:offer_id>/delete', methods=['DELETE'])
@login_required
@admin_required
def delete_offer(offer_id):
    """Supprimer une offre"""
    try:
        offer = FreightOffer.query.get_or_404(offer_id)
        offer_title = offer.title

        # Suppression des propositions associées
        FreightProposal.query.filter_by(freight_offer_id=offer.id).delete()

        # Suppression des missions associées
        Mission.query.filter_by(freight_offer_id=offer.id).delete()

        # Suppression de l'offre
        db.session.delete(offer)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Offre "{offer_title}" supprimée avec succès'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'Erreur lors de la suppression: {str(e)}'})

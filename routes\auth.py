from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash
from models.user import User
from app import db
import re

auth_bp = Blueprint('auth', __name__)

def validate_email(email):
    """Valide le format de l'email"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """Valide la force du mot de passe"""
    if len(password) < 8:
        return False, "Le mot de passe doit contenir au moins 8 caractères"
    if not re.search(r'[A-Z]', password):
        return False, "Le mot de passe doit contenir au moins une majuscule"
    if not re.search(r'[a-z]', password):
        return False, "Le mot de passe doit contenir au moins une minuscule"
    if not re.search(r'\d', password):
        return False, "Le mot de passe doit contenir au moins un chiffre"
    return True, "Mot de passe valide"

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Page de connexion"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        remember_me = bool(request.form.get('remember_me'))
        
        # Validation des données
        if not email or not password:
            flash('Veuillez remplir tous les champs', 'error')
            return render_template('auth/login.html')
        
        if not validate_email(email):
            flash('Format d\'email invalide', 'error')
            return render_template('auth/login.html')
        
        # Recherche de l'utilisateur
        user = User.query.filter_by(email=email).first()
        
        if user and user.check_password(password):
            if not user.is_active:
                flash('Votre compte a été désactivé. Contactez l\'administrateur.', 'error')
                return render_template('auth/login.html')
            
            # Connexion réussie
            login_user(user, remember=remember_me)
            user.last_login = db.func.now()
            db.session.commit()
            
            # Redirection vers la page demandée ou le dashboard
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            
            flash(f'Bienvenue {user.get_full_name()}!', 'success')
            return redirect(url_for('dashboard.index'))
        else:
            flash('Email ou mot de passe incorrect', 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """Page d'inscription"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    if request.method == 'POST':
        # Récupération des données du formulaire
        email = request.form.get('email', '').strip().lower()
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')
        first_name = request.form.get('first_name', '').strip()
        last_name = request.form.get('last_name', '').strip()
        phone = request.form.get('phone', '').strip()
        user_type = request.form.get('user_type', '')
        company_name = request.form.get('company_name', '').strip()
        
        # Validation des données
        errors = []
        
        if not all([email, username, password, first_name, last_name, user_type]):
            errors.append('Veuillez remplir tous les champs obligatoires')
        
        if not validate_email(email):
            errors.append('Format d\'email invalide')
        
        if len(username) < 3:
            errors.append('Le nom d\'utilisateur doit contenir au moins 3 caractères')
        
        is_valid_password, password_message = validate_password(password)
        if not is_valid_password:
            errors.append(password_message)
        
        if password != confirm_password:
            errors.append('Les mots de passe ne correspondent pas')
        
        if user_type not in ['transporteur', 'expediteur', 'chauffeur']:
            errors.append('Type d\'utilisateur invalide')
        
        # Vérification de l'unicité
        if User.query.filter_by(email=email).first():
            errors.append('Cette adresse email est déjà utilisée')
        
        if User.query.filter_by(username=username).first():
            errors.append('Ce nom d\'utilisateur est déjà pris')
        
        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('auth/register.html')
        
        # Création de l'utilisateur
        try:
            user = User(
                email=email,
                username=username,
                first_name=first_name,
                last_name=last_name,
                phone=phone,
                user_type=user_type,
                company_name=company_name if company_name else None
            )
            user.set_password(password)
            
            db.session.add(user)
            db.session.commit()
            
            flash('Inscription réussie! Vous pouvez maintenant vous connecter.', 'success')
            return redirect(url_for('auth.login'))
            
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de l\'inscription. Veuillez réessayer.', 'error')
            return render_template('auth/register.html')
    
    return render_template('auth/register.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """Déconnexion"""
    logout_user()
    flash('Vous avez été déconnecté avec succès', 'info')
    return redirect(url_for('index'))

@auth_bp.route('/profile')
@login_required
def profile():
    """Page de profil utilisateur"""
    return render_template('auth/profile.html', user=current_user)

@auth_bp.route('/profile/edit', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """Modification du profil utilisateur"""
    if request.method == 'POST':
        # Récupération des données
        first_name = request.form.get('first_name', '').strip()
        last_name = request.form.get('last_name', '').strip()
        phone = request.form.get('phone', '').strip()
        company_name = request.form.get('company_name', '').strip()
        address = request.form.get('address', '').strip()
        city = request.form.get('city', '').strip()
        postal_code = request.form.get('postal_code', '').strip()
        
        # Validation
        if not first_name or not last_name:
            flash('Le prénom et le nom sont obligatoires', 'error')
            return render_template('auth/edit_profile.html', user=current_user)
        
        # Mise à jour
        try:
            current_user.first_name = first_name
            current_user.last_name = last_name
            current_user.phone = phone
            current_user.company_name = company_name if company_name else None
            current_user.address = address if address else None
            current_user.city = city if city else None
            current_user.postal_code = postal_code if postal_code else None
            
            db.session.commit()
            flash('Profil mis à jour avec succès', 'success')
            return redirect(url_for('auth.profile'))
            
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la mise à jour du profil', 'error')
    
    return render_template('auth/edit_profile.html', user=current_user)

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Changement de mot de passe"""
    if request.method == 'POST':
        current_password = request.form.get('current_password', '')
        new_password = request.form.get('new_password', '')
        confirm_password = request.form.get('confirm_password', '')
        
        # Validation
        if not current_user.check_password(current_password):
            flash('Mot de passe actuel incorrect', 'error')
            return render_template('auth/change_password.html')
        
        is_valid, message = validate_password(new_password)
        if not is_valid:
            flash(message, 'error')
            return render_template('auth/change_password.html')
        
        if new_password != confirm_password:
            flash('Les nouveaux mots de passe ne correspondent pas', 'error')
            return render_template('auth/change_password.html')
        
        # Mise à jour
        try:
            current_user.set_password(new_password)
            db.session.commit()
            flash('Mot de passe modifié avec succès', 'success')
            return redirect(url_for('auth.profile'))
            
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors du changement de mot de passe', 'error')
    
    return render_template('auth/change_password.html')

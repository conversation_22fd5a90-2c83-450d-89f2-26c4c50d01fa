{% extends "base.html" %}

{% block title %}Dashboard - SABTRANS{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="h3 text-primary">
                    <i class="fas fa-tachometer-alt me-2"></i>Bienvenue sur SABTRANS
                </h1>
                <p class="text-muted">Votre plateforme de transport et logistique</p>
            </div>

            <!-- Informations utilisateur -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>Informations de Compte
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Nom :</strong> {{ current_user.get_full_name() }}</p>
                            <p><strong>Email :</strong> {{ current_user.email }}</p>
                            <p><strong>Type de compte :</strong> 
                                <span class="badge bg-secondary">{{ current_user.user_type.title() }}</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            {% if current_user.company_name %}
                                <p><strong>Entreprise :</strong> {{ current_user.company_name }}</p>
                            {% endif %}
                            <p><strong>Membre depuis :</strong> {{ current_user.created_at.strftime('%d/%m/%Y') }}</p>
                            <p><strong>Statut :</strong> 
                                {% if current_user.is_verified %}
                                    <span class="badge bg-success">Vérifié</span>
                                {% else %}
                                    <span class="badge bg-warning">En attente de vérification</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Actions Rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="d-grid">
                                <a href="{{ url_for('freight.index') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-exchange-alt me-2"></i>Bourse de Fret
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-grid">
                                <a href="{{ url_for('documents.index') }}" class="btn btn-outline-success">
                                    <i class="fas fa-file-alt me-2"></i>Mes Documents
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-grid">
                                <a href="{{ url_for('freight.create') }}" class="btn btn-outline-warning">
                                    <i class="fas fa-plus me-2"></i>Nouvelle Offre
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-grid">
                                <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-info">
                                    <i class="fas fa-user-edit me-2"></i>Mon Profil
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Fonctionnalités disponibles -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>Fonctionnalités Disponibles
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="feature-item">
                                <h6><i class="fas fa-exchange-alt text-primary me-2"></i>Bourse de Fret</h6>
                                <p class="text-muted small">Publiez et recherchez des offres de transport</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="feature-item">
                                <h6><i class="fas fa-file-signature text-success me-2"></i>Gestion Documentaire</h6>
                                <p class="text-muted small">Gérez vos documents avec signature électronique</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="feature-item">
                                <h6><i class="fas fa-map-marker-alt text-warning me-2"></i>Suivi en Temps Réel</h6>
                                <p class="text-muted small">Suivez vos missions et marchandises</p>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="feature-item">
                                <h6><i class="fas fa-mobile-alt text-info me-2"></i>Application Mobile</h6>
                                <p class="text-muted small">Interface optimisée pour mobile</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modules avancés -->
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-puzzle-piece me-2"></i>Modules Spécialisés
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4 mb-3">
                            <div class="module-item">
                                <div class="bg-primary text-white rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <i class="fas fa-lock"></i>
                                </div>
                                <h6>SAB Private</h6>
                                <p class="text-muted small">Bourse privée</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="module-item">
                                <div class="bg-success text-white rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <i class="fas fa-route"></i>
                                </div>
                                <h6>SAB Optim</h6>
                                <p class="text-muted small">Optimisation d'itinéraires</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="module-item">
                                <div class="bg-warning text-white rounded-circle mx-auto mb-2 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <i class="fas fa-file-invoice"></i>
                                </div>
                                <h6>SAB Fac</h6>
                                <p class="text-muted small">Facturation automatique</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aide et support -->
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Aide et Support
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-book text-primary me-2"></i>Documentation</h6>
                            <p class="text-muted small">Consultez notre guide d'utilisation complet</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-headset text-success me-2"></i>Support Client</h6>
                            <p class="text-muted small">Notre équipe est là pour vous aider</p>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <a href="#" class="btn btn-outline-primary me-2">
                            <i class="fas fa-book me-1"></i>Guide d'utilisation
                        </a>
                        <a href="#" class="btn btn-outline-success">
                            <i class="fas fa-envelope me-1"></i>Contacter le support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Message de bienvenue personnalisé
document.addEventListener('DOMContentLoaded', function() {
    const userType = '{{ current_user.user_type }}';
    const userName = '{{ current_user.get_full_name() }}';
    
    // Afficher un message personnalisé selon le type d'utilisateur
    setTimeout(function() {
        let message = '';
        switch(userType) {
            case 'transporteur':
                message = 'En tant que transporteur, vous pouvez rechercher des chargements et gérer vos missions.';
                break;
            case 'expediteur':
                message = 'En tant qu\'expéditeur, vous pouvez publier des demandes de transport et suivre vos expéditions.';
                break;
            case 'chauffeur':
                message = 'En tant que chauffeur, vous pouvez accéder à vos missions et gérer vos documents.';
                break;
            case 'admin':
                message = 'En tant qu\'administrateur, vous avez accès à toutes les fonctionnalités de gestion.';
                break;
            default:
                message = 'Bienvenue sur SABTRANS ! Explorez toutes nos fonctionnalités.';
        }
        
        showNotification(message, 'info');
    }, 1000);
});
</script>
{% endblock %}

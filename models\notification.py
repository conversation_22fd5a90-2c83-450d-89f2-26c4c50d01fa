from datetime import datetime
from database import db

class Notification(db.Model):
    """Système de notifications pour les utilisateurs"""
    __tablename__ = 'notifications'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    
    # Utilisateur destinataire
    user_id = db.<PERSON>umn(db.<PERSON><PERSON>, db.<PERSON>('users.id'), nullable=False)
    
    # Type de notification
    notification_type = db.Column(db.Enum(
        'proposal_accepted',    # Proposition acceptée
        'proposal_rejected',    # Proposition rejetée
        'new_proposal',        # Nouvelle proposition reçue
        'mission_created',     # Mission créée
        'mission_updated',     # Mission mise à jour
        'payment_received',    # Paiement reçu
        'system_message'       # Message système
    ), nullable=False)
    
    # Contenu de la notification
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    
    # Références optionnelles
    freight_offer_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('freight_offers.id'), nullable=True)
    freight_proposal_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('freight_proposals.id'), nullable=True)
    mission_id = db.Column(db.Integer, db.ForeignKey('missions.id'), nullable=True)
    
    # Statut de la notification
    is_read = db.Column(db.Boolean, default=False)
    is_archived = db.Column(db.Boolean, default=False)
    
    # Dates
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    read_at = db.Column(db.DateTime)
    
    # Relations
    user = db.relationship('User', backref='notifications')
    freight_offer = db.relationship('FreightOffer', backref='notifications')
    freight_proposal = db.relationship('FreightProposal', backref='notifications')
    mission = db.relationship('Mission', backref='notifications')
    
    def mark_as_read(self):
        """Marquer la notification comme lue"""
        self.is_read = True
        self.read_at = datetime.utcnow()
        db.session.commit()
    
    def archive(self):
        """Archiver la notification"""
        self.is_archived = True
        db.session.commit()
    
    def get_icon(self):
        """Retourne l'icône appropriée selon le type"""
        icons = {
            'proposal_accepted': 'fas fa-check-circle text-success',
            'proposal_rejected': 'fas fa-times-circle text-danger',
            'new_proposal': 'fas fa-euro-sign text-primary',
            'mission_created': 'fas fa-truck text-info',
            'mission_updated': 'fas fa-sync text-warning',
            'payment_received': 'fas fa-money-bill text-success',
            'system_message': 'fas fa-info-circle text-secondary'
        }
        return icons.get(self.notification_type, 'fas fa-bell')
    
    def get_url(self):
        """Retourne l'URL de redirection selon le type"""
        if self.notification_type in ['proposal_accepted', 'proposal_rejected'] and self.freight_proposal_id:
            return f'/freight/{self.freight_offer_id}'
        elif self.notification_type == 'new_proposal' and self.freight_offer_id:
            return f'/freight/{self.freight_offer_id}'
        elif self.notification_type in ['mission_created', 'mission_updated'] and self.mission_id:
            return f'/missions/{self.mission_id}'
        return '/dashboard'
    
    def to_dict(self):
        """Convertit en dictionnaire pour l'API"""
        return {
            'id': self.id,
            'type': self.notification_type,
            'title': self.title,
            'message': self.message,
            'is_read': self.is_read,
            'icon': self.get_icon(),
            'url': self.get_url(),
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'read_at': self.read_at.isoformat() if self.read_at else None
        }
    
    @staticmethod
    def create_notification(user_id, notification_type, title, message, 
                          freight_offer_id=None, freight_proposal_id=None, mission_id=None):
        """Créer une nouvelle notification"""
        notification = Notification(
            user_id=user_id,
            notification_type=notification_type,
            title=title,
            message=message,
            freight_offer_id=freight_offer_id,
            freight_proposal_id=freight_proposal_id,
            mission_id=mission_id
        )
        db.session.add(notification)
        db.session.commit()
        return notification
    
    def __repr__(self):
        return f'<Notification {self.title}>'

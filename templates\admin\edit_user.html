{% extends "base.html" %}

{% block title %}Modifier {{ user.get_full_name() }} - SABTRANS{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-10 mx-auto">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-user-edit me-2"></i>Modifier {{ user.get_full_name() }}
                </h1>
                <div>
                    <a href="{{ url_for('admin.user_detail', user_id=user.id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour au profil
                    </a>
                    <a href="{{ url_for('admin.users') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>Liste des utilisateurs
                    </a>
                </div>
            </div>

            <form method="POST" class="needs-validation" novalidate>
                <!-- Informations personnelles -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>Informations Personnelles
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="{{ user.first_name }}" required>
                                <div class="invalid-feedback">Veuillez saisir le prénom</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="{{ user.last_name }}" required>
                                <div class="invalid-feedback">Veuillez saisir le nom</div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ user.email }}" required>
                                <div class="invalid-feedback">Veuillez saisir un email valide</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Téléphone</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ user.phone or '' }}" placeholder="+33 1 23 45 67 89">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">Nom d'utilisateur *</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="{{ user.username }}" required>
                                <div class="form-text">Au moins 3 caractères, uniquement lettres, chiffres et tirets</div>
                                <div class="invalid-feedback">Veuillez saisir un nom d'utilisateur valide</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="user_type" class="form-label">Type d'utilisateur *</label>
                                <select class="form-select" id="user_type" name="user_type" required>
                                    <option value="transporteur" {% if user.user_type == 'transporteur' %}selected{% endif %}>Transporteur</option>
                                    <option value="expediteur" {% if user.user_type == 'expediteur' %}selected{% endif %}>Expéditeur</option>
                                    <option value="chauffeur" {% if user.user_type == 'chauffeur' %}selected{% endif %}>Chauffeur</option>
                                    <option value="admin" {% if user.user_type == 'admin' %}selected{% endif %}>Administrateur</option>
                                </select>
                                <div class="invalid-feedback">Veuillez sélectionner un type d'utilisateur</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Informations entreprise -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-building me-2"></i>Informations Entreprise
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="company_name" class="form-label">Nom de l'entreprise</label>
                                <input type="text" class="form-control" id="company_name" name="company_name" 
                                       value="{{ user.company_name or '' }}" placeholder="SARL Transport Express">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="siret" class="form-label">SIRET</label>
                                <input type="text" class="form-control" id="siret" name="siret" 
                                       value="{{ user.siret or '' }}" placeholder="12345678901234">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Adresse</label>
                            <textarea class="form-control" id="address" name="address" rows="2" 
                                      placeholder="123 Rue de la Logistique">{{ user.address or '' }}</textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="postal_code" class="form-label">Code postal</label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code" 
                                       value="{{ user.postal_code or '' }}" placeholder="75001">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="city" class="form-label">Ville</label>
                                <input type="text" class="form-control" id="city" name="city" 
                                       value="{{ user.city or '' }}" placeholder="Paris">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="country" class="form-label">Pays</label>
                                <select class="form-select" id="country" name="country">
                                    <option value="France" {% if user.country == 'France' %}selected{% endif %}>France</option>
                                    <option value="Belgique" {% if user.country == 'Belgique' %}selected{% endif %}>Belgique</option>
                                    <option value="Suisse" {% if user.country == 'Suisse' %}selected{% endif %}>Suisse</option>
                                    <option value="Luxembourg" {% if user.country == 'Luxembourg' %}selected{% endif %}>Luxembourg</option>
                                    <option value="Espagne" {% if user.country == 'Espagne' %}selected{% endif %}>Espagne</option>
                                    <option value="Italie" {% if user.country == 'Italie' %}selected{% endif %}>Italie</option>
                                    <option value="Allemagne" {% if user.country == 'Allemagne' %}selected{% endif %}>Allemagne</option>
                                    <option value="Autre" {% if user.country not in ['France', 'Belgique', 'Suisse', 'Luxembourg', 'Espagne', 'Italie', 'Allemagne'] %}selected{% endif %}>Autre</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Paramètres du compte -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>Paramètres du Compte
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Statut du compte</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           {% if user.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        <strong>Compte actif</strong>
                                    </label>
                                    <div class="form-text">Si décoché, l'utilisateur ne pourra pas se connecter</div>
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_verified" name="is_verified" 
                                           {% if user.is_verified %}checked{% endif %}>
                                    <label class="form-check-label" for="is_verified">
                                        <strong>Compte vérifié</strong>
                                    </label>
                                    <div class="form-text">Compte validé par l'administration</div>
                                </div>
                                
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_confirmed" name="email_confirmed" 
                                           {% if user.email_confirmed %}checked{% endif %}>
                                    <label class="form-check-label" for="email_confirmed">
                                        <strong>Email confirmé</strong>
                                    </label>
                                    <div class="form-text">L'utilisateur a confirmé son adresse email</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>Informations système</h6>
                                <div class="mb-3">
                                    <label class="form-label text-muted">ID Utilisateur</label>
                                    <div class="form-control-plaintext"><code>{{ user.id }}</code></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted">Membre depuis</label>
                                    <div class="form-control-plaintext">{{ user.created_at.strftime('%d/%m/%Y à %H:%M') }}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label text-muted">Dernière connexion</label>
                                    <div class="form-control-plaintext">
                                        {% if user.last_login %}
                                            {{ user.last_login.strftime('%d/%m/%Y à %H:%M') }}
                                        {% else %}
                                            <span class="text-muted">Jamais</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions de sécurité -->
                <div class="card mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-shield-alt me-2"></i>Actions de Sécurité
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Mot de passe</h6>
                                <p class="text-muted">
                                    Dernière modification : {{ user.updated_at.strftime('%d/%m/%Y') }}
                                </p>
                                <button type="button" class="btn btn-outline-warning" onclick="resetPassword()">
                                    <i class="fas fa-key me-2"></i>Réinitialiser le mot de passe
                                </button>
                            </div>
                            
                            <div class="col-md-6">
                                <h6>Sessions</h6>
                                <p class="text-muted">
                                    Gérer les sessions actives de l'utilisateur
                                </p>
                                <button type="button" class="btn btn-outline-danger" onclick="revokeAllSessions()">
                                    <i class="fas fa-sign-out-alt me-2"></i>Déconnecter partout
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes administrateur -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-sticky-note me-2"></i>Notes Administrateur
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">Notes internes</label>
                            <textarea class="form-control" id="admin_notes" name="admin_notes" rows="4" 
                                      placeholder="Notes visibles uniquement par les administrateurs...">{{ user.admin_notes or '' }}</textarea>
                            <div class="form-text">Ces notes ne sont visibles que par les administrateurs</div>
                        </div>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-between mb-4">
                    <div>
                        <a href="{{ url_for('admin.user_detail', user_id=user.id) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-eye me-2"></i>Voir le profil
                        </a>
                        <button type="button" class="btn btn-outline-info" onclick="sendNotification()">
                            <i class="fas fa-bell me-2"></i>Envoyer notification
                        </button>
                    </div>
                    <div>
                        <a href="{{ url_for('admin.users') }}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>Sauvegarder les modifications
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Validation du nom d'utilisateur
document.getElementById('username').addEventListener('input', function() {
    const username = this.value;
    const pattern = /^[a-zA-Z0-9_-]{3,}$/;
    
    if (username.length >= 3 && pattern.test(username)) {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
    } else {
        this.classList.remove('is-valid');
        this.classList.add('is-invalid');
    }
});

// Validation de l'email
document.getElementById('email').addEventListener('input', function() {
    const email = this.value;
    const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (pattern.test(email)) {
        this.classList.remove('is-invalid');
        this.classList.add('is-valid');
    } else {
        this.classList.remove('is-valid');
        this.classList.add('is-invalid');
    }
});

// Formatage automatique du SIRET
document.getElementById('siret').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, '');
    if (value.length > 14) {
        value = value.substring(0, 14);
    }
    this.value = value;
});

// Formatage automatique du code postal
document.getElementById('postal_code').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, '');
    if (value.length > 5) {
        value = value.substring(0, 5);
    }
    this.value = value;
});

// Réinitialiser le mot de passe
function resetPassword() {
    if (confirm('Générer un nouveau mot de passe temporaire pour cet utilisateur ?')) {
        fetch(`/admin/users/{{ user.id }}/reset-password`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Nouveau mot de passe temporaire : ${data.new_password}\n\nCommuniquez-le à l'utilisateur de manière sécurisée.`);
                showNotification('Mot de passe réinitialisé avec succès', 'success');
            } else {
                showNotification('Erreur lors de la réinitialisation', 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de la réinitialisation', 'error');
        });
    }
}

// Révoquer toutes les sessions
function revokeAllSessions() {
    if (confirm('Déconnecter cet utilisateur de toutes ses sessions actives ?')) {
        fetch(`/admin/users/{{ user.id }}/revoke-sessions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Sessions révoquées avec succès', 'success');
            } else {
                showNotification('Erreur lors de la révocation', 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de la révocation', 'error');
        });
    }
}

// Envoyer une notification
function sendNotification() {
    const message = prompt('Message de notification à envoyer :');
    if (message) {
        fetch(`/admin/users/{{ user.id }}/send-notification`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ message: message })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Notification envoyée avec succès', 'success');
            } else {
                showNotification('Erreur lors de l\'envoi', 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de l\'envoi', 'error');
        });
    }
}

// Confirmation avant sauvegarde
document.querySelector('form').addEventListener('submit', function(e) {
    if (!confirm('Êtes-vous sûr de vouloir sauvegarder ces modifications ?')) {
        e.preventDefault();
    }
});
</script>
{% endblock %}

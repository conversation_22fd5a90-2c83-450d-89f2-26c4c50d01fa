#!/usr/bin/env python3
"""
Utilitaires pour la gestion des devises dans SABTRANS
Support du Dinar Algérien (DZD) et autres devises
"""

# Devises supportées par SABTRANS
SUPPORTED_CURRENCIES = {
    'DZD': {
        'name': 'Dinar Algérien',
        'symbol': 'د.ج',
        'code': 'DZD',
        'decimal_places': 2,
        'format': '{amount} {symbol}',
        'locale': 'ar_DZ'
    },
    'EUR': {
        'name': 'Euro',
        'symbol': '€',
        'code': 'EUR',
        'decimal_places': 2,
        'format': '{amount}{symbol}',
        'locale': 'fr_FR'
    },
    'USD': {
        'name': 'Dollar Américain',
        'symbol': '$',
        'code': 'USD',
        'decimal_places': 2,
        'format': '${amount}',
        'locale': 'en_US'
    },
    'MAD': {
        'name': '<PERSON>rham Marocain',
        'symbol': 'DH',
        'code': 'MAD',
        'decimal_places': 2,
        'format': '{amount} {symbol}',
        'locale': 'ar_MA'
    },
    'TND': {
        'name': 'Dinar Tunisien',
        'symbol': 'د.ت',
        'code': 'TND',
        'decimal_places': 3,
        'format': '{amount} {symbol}',
        'locale': 'ar_TN'
    }
}

# Devise par défaut pour l'Algérie
DEFAULT_CURRENCY = 'DZD'

def get_currency_choices():
    """Retourne la liste des devises pour les formulaires"""
    return [(code, f"{info['name']} ({info['symbol']})") for code, info in SUPPORTED_CURRENCIES.items()]

def get_currency_info(currency_code):
    """Retourne les informations d'une devise"""
    return SUPPORTED_CURRENCIES.get(currency_code, SUPPORTED_CURRENCIES[DEFAULT_CURRENCY])

def format_currency(amount, currency_code=DEFAULT_CURRENCY):
    """Formate un montant avec la devise appropriée"""
    if amount is None:
        return "N/A"
    
    currency_info = get_currency_info(currency_code)
    
    # Arrondir selon le nombre de décimales de la devise
    decimal_places = currency_info['decimal_places']
    rounded_amount = round(float(amount), decimal_places)
    
    # Formater le nombre avec les décimales appropriées
    if decimal_places == 0:
        formatted_amount = f"{int(rounded_amount):,}"
    else:
        formatted_amount = f"{rounded_amount:,.{decimal_places}f}"
    
    # Appliquer le format de la devise
    return currency_info['format'].format(
        amount=formatted_amount,
        symbol=currency_info['symbol']
    )

def get_currency_symbol(currency_code=DEFAULT_CURRENCY):
    """Retourne le symbole d'une devise"""
    return get_currency_info(currency_code)['symbol']

def convert_currency_display(amount, from_currency, to_currency=DEFAULT_CURRENCY):
    """Convertit l'affichage d'une devise à une autre (sans taux de change réel)"""
    # Note: Dans une vraie application, on utiliserait une API de taux de change
    # Ici on se contente de changer le format d'affichage
    return format_currency(amount, to_currency)

def validate_currency_code(currency_code):
    """Valide qu'un code de devise est supporté"""
    return currency_code in SUPPORTED_CURRENCIES

def get_currency_for_country(country_code):
    """Retourne la devise par défaut pour un pays"""
    country_currencies = {
        'DZ': 'DZD',  # Algérie
        'FR': 'EUR',  # France
        'MA': 'MAD',  # Maroc
        'TN': 'TND',  # Tunisie
        'US': 'USD',  # États-Unis
    }
    return country_currencies.get(country_code, DEFAULT_CURRENCY)

# Taux de change approximatifs (à remplacer par une API en production)
EXCHANGE_RATES = {
    'DZD': 1.0,      # Base
    'EUR': 0.0074,   # 1 DZD = 0.0074 EUR
    'USD': 0.0075,   # 1 DZD = 0.0075 USD
    'MAD': 0.075,    # 1 DZD = 0.075 MAD
    'TND': 0.023,    # 1 DZD = 0.023 TND
}

def convert_amount(amount, from_currency, to_currency):
    """Convertit un montant d'une devise à une autre (approximatif)"""
    if from_currency == to_currency:
        return amount
    
    if amount is None or amount == 0:
        return 0
    
    # Convertir vers DZD d'abord
    if from_currency != 'DZD':
        amount_dzd = amount / EXCHANGE_RATES.get(from_currency, 1)
    else:
        amount_dzd = amount
    
    # Puis convertir vers la devise cible
    if to_currency != 'DZD':
        result = amount_dzd * EXCHANGE_RATES.get(to_currency, 1)
    else:
        result = amount_dzd
    
    return round(result, get_currency_info(to_currency)['decimal_places'])

def get_price_range_text(min_price, max_price, currency_code=DEFAULT_CURRENCY):
    """Retourne un texte de fourchette de prix"""
    if min_price is None and max_price is None:
        return "Prix à négocier"
    elif min_price is None:
        return f"Jusqu'à {format_currency(max_price, currency_code)}"
    elif max_price is None:
        return f"À partir de {format_currency(min_price, currency_code)}"
    elif min_price == max_price:
        return format_currency(min_price, currency_code)
    else:
        return f"{format_currency(min_price, currency_code)} - {format_currency(max_price, currency_code)}"

# Fonction pour les templates Jinja2
def register_currency_filters(app):
    """Enregistre les filtres de devise pour les templates"""
    
    @app.template_filter('currency')
    def currency_filter(amount, currency_code=DEFAULT_CURRENCY):
        return format_currency(amount, currency_code)
    
    @app.template_filter('currency_symbol')
    def currency_symbol_filter(currency_code=DEFAULT_CURRENCY):
        return get_currency_symbol(currency_code)
    
    @app.template_global()
    def get_supported_currencies():
        return SUPPORTED_CURRENCIES
    
    @app.template_global()
    def get_default_currency():
        return DEFAULT_CURRENCY

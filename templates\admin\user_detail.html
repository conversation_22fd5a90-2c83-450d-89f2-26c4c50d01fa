{% extends "base.html" %}

{% block title %}{{ user.get_full_name() }} - Gestion Utilisateurs - SABTRANS{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-10 mx-auto">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-user me-2"></i>{{ user.get_full_name() }}
                </h1>
                <div>
                    <a href="{{ url_for('admin.users') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                    <a href="{{ url_for('admin.edit_user', user_id=user.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Modifier
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Informations principales -->
                <div class="col-lg-8 mb-4">
                    <!-- Profil utilisateur -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-id-card me-2"></i>Informations Personnelles
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 text-center mb-3">
                                    <div class="avatar-large mx-auto mb-3">
                                        {{ user.first_name[0] }}{{ user.last_name[0] }}
                                    </div>
                                    <h5>{{ user.get_full_name() }}</h5>
                                    <p class="text-muted">@{{ user.username }}</p>
                                    <span class="badge bg-{{ 'primary' if user.user_type == 'admin' else 'secondary' }} fs-6">
                                        {% if user.user_type %}
                                            {{ user.user_type.title() }}
                                        {% else %}
                                            Type non défini
                                        {% endif %}
                                    </span>
                                </div>
                                <div class="col-md-9">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted">Email</label>
                                            <p>
                                                {{ user.email }}
                                                {% if user.email_confirmed %}
                                                    <i class="fas fa-check-circle text-success ms-1" title="Email confirmé"></i>
                                                {% else %}
                                                    <i class="fas fa-exclamation-circle text-warning ms-1" title="Email non confirmé"></i>
                                                {% endif %}
                                            </p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted">Téléphone</label>
                                            <p>{{ user.phone or 'Non renseigné' }}</p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted">Statut du compte</label>
                                            <p>
                                                {% if user.is_active %}
                                                    <span class="badge bg-success">Actif</span>
                                                {% else %}
                                                    <span class="badge bg-danger">Inactif</span>
                                                {% endif %}
                                                {% if user.is_verified %}
                                                    <span class="badge bg-info ms-1">Vérifié</span>
                                                {% else %}
                                                    <span class="badge bg-warning ms-1">Non vérifié</span>
                                                {% endif %}
                                            </p>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label text-muted">Type de compte</label>
                                            <p>
                                                <span class="badge bg-{{ 'primary' if user.user_type == 'admin' else 'secondary' }}">
                                                    {% if user.user_type %}
                                                        {{ user.user_type.title() }}
                                                    {% else %}
                                                        Type non défini
                                                    {% endif %}
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations entreprise -->
                    {% if user.company_name or user.address %}
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-building me-2"></i>Informations Entreprise
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% if user.company_name %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Entreprise</label>
                                    <p class="fw-bold">{{ user.company_name }}</p>
                                </div>
                                {% endif %}
                                {% if user.siret %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">SIRET</label>
                                    <p>{{ user.siret }}</p>
                                </div>
                                {% endif %}
                                {% if user.address %}
                                <div class="col-12 mb-3">
                                    <label class="form-label text-muted">Adresse</label>
                                    <p>
                                        {{ user.address }}<br>
                                        {% if user.postal_code %}{{ user.postal_code }} {% endif %}
                                        {% if user.city %}{{ user.city }}{% endif %}
                                        {% if user.country %}, {{ user.country }}{% endif %}
                                    </p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Activité récente -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-line me-2"></i>Activité et Statistiques
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3 mb-3">
                                    <div class="stat-item">
                                        <h3 class="stat-number text-primary">{{ user_stats.total_offers or 0 }}</h3>
                                        <p class="text-muted">Offres publiées</p>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="stat-item">
                                        <h3 class="stat-number text-success">{{ user_stats.total_missions or 0 }}</h3>
                                        <p class="text-muted">Missions</p>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="stat-item">
                                        <h3 class="stat-number text-warning">{{ user_stats.total_documents or 0 }}</h3>
                                        <p class="text-muted">Documents</p>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="stat-item">
                                        <h3 class="stat-number text-info">
                                            {% if user_stats.avg_rating %}
                                                {{ "%.1f"|format(user_stats.avg_rating) }}/5
                                            {% else %}
                                                -
                                            {% endif %}
                                        </h3>
                                        <p class="text-muted">Note moyenne</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Offres récentes -->
                    {% if recent_offers %}
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-list me-2"></i>Offres Récentes
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                {% for offer in recent_offers %}
                                <div class="list-group-item d-flex justify-content-between align-items-start">
                                    <div class="ms-2 me-auto">
                                        <div class="fw-bold">{{ offer.title }}</div>
                                        <small class="text-muted">
                                            {{ offer.pickup_city }} → {{ offer.delivery_city }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge bg-{{ 'primary' if offer.offer_type == 'demande' else 'success' }}">
                                            {% if offer.offer_type %}
                                                {{ offer.offer_type.title() }}
                                            {% else %}
                                                Type non défini
                                            {% endif %}
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            {% if offer.created_at %}
                                                {{ offer.created_at.strftime('%d/%m %H:%M') }}
                                            {% else %}
                                                Date inconnue
                                            {% endif %}
                                        </small>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Informations système -->
                <div class="col-lg-4">
                    <!-- Dates importantes -->
                    <div class="card mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar me-2"></i>Informations Système
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label text-muted">Membre depuis</label>
                                <p>{{ user.created_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Dernière connexion</label>
                                <p>
                                    {% if user.last_login %}
                                        {{ user.last_login.strftime('%d/%m/%Y à %H:%M') }}
                                    {% else %}
                                        <span class="text-muted">Jamais</span>
                                    {% endif %}
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Dernière modification</label>
                                <p>{{ user.updated_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">ID Utilisateur</label>
                                <p><code>{{ user.id }}</code></p>
                            </div>
                        </div>
                    </div>

                    <!-- Actions administrateur -->
                    <div class="card mb-4">
                        <div class="card-header bg-dark text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-tools me-2"></i>Actions Administrateur
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('admin.edit_user', user_id=user.id) }}" class="btn btn-warning">
                                    <i class="fas fa-edit me-2"></i>Modifier le profil
                                </a>
                                
                                {% if user.is_active %}
                                <button class="btn btn-outline-danger" onclick="toggleUserStatus({{ user.id }}, false)">
                                    <i class="fas fa-user-slash me-2"></i>Désactiver le compte
                                </button>
                                {% else %}
                                <button class="btn btn-outline-success" onclick="toggleUserStatus({{ user.id }}, true)">
                                    <i class="fas fa-user-check me-2"></i>Activer le compte
                                </button>
                                {% endif %}
                                
                                {% if not user.is_verified %}
                                <button class="btn btn-outline-info" onclick="verifyUser({{ user.id }})">
                                    <i class="fas fa-certificate me-2"></i>Vérifier le compte
                                </button>
                                {% endif %}
                                
                                {% if not user.email_confirmed %}
                                <button class="btn btn-outline-warning" onclick="confirmEmail({{ user.id }})">
                                    <i class="fas fa-envelope-check me-2"></i>Confirmer l'email
                                </button>
                                {% endif %}
                                
                                <button class="btn btn-outline-primary" onclick="resetPassword({{ user.id }})">
                                    <i class="fas fa-key me-2"></i>Réinitialiser mot de passe
                                </button>
                                
                                <hr>
                                
                                <button class="btn btn-outline-info" onclick="sendMessage({{ user.id }})">
                                    <i class="fas fa-envelope me-2"></i>Envoyer un message
                                </button>
                                
                                <button class="btn btn-outline-secondary" onclick="viewLogs({{ user.id }})">
                                    <i class="fas fa-history me-2"></i>Voir les logs
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Alertes et notes -->
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>Alertes et Notes
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if not user.email_confirmed %}
                            <div class="alert alert-warning alert-sm">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Email non confirmé
                            </div>
                            {% endif %}
                            
                            {% if not user.is_verified %}
                            <div class="alert alert-info alert-sm">
                                <i class="fas fa-info-circle me-2"></i>
                                Compte non vérifié
                            </div>
                            {% endif %}
                            
                            {% if not user.is_active %}
                            <div class="alert alert-danger alert-sm">
                                <i class="fas fa-ban me-2"></i>
                                Compte désactivé
                            </div>
                            {% endif %}
                            
                            {% if not user.last_login %}
                            <div class="alert alert-secondary alert-sm">
                                <i class="fas fa-user-clock me-2"></i>
                                Jamais connecté
                            </div>
                            {% endif %}
                            
                            <!-- Zone pour ajouter des notes admin -->
                            <div class="mt-3">
                                <label for="admin_notes" class="form-label">Notes administrateur</label>
                                <textarea class="form-control" id="admin_notes" rows="3" 
                                          placeholder="Ajouter des notes sur cet utilisateur..."></textarea>
                                <button class="btn btn-sm btn-outline-primary mt-2" onclick="saveNotes({{ user.id }})">
                                    <i class="fas fa-save me-1"></i>Sauvegarder
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #003366, #90EE90);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 24px;
}

.stat-item {
    padding: 1rem;
    border-radius: 8px;
    background: rgba(0,0,0,0.02);
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    margin: 0;
}

.alert-sm {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function toggleUserStatus(userId, activate) {
    const action = activate ? 'activer' : 'désactiver';
    if (confirm(`Êtes-vous sûr de vouloir ${action} cet utilisateur ?`)) {
        fetch(`/admin/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ active: activate })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Utilisateur ${action} avec succès`, 'success');
                location.reload();
            } else {
                showNotification('Erreur lors de la modification', 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de la modification', 'error');
        });
    }
}

function verifyUser(userId) {
    if (confirm('Êtes-vous sûr de vouloir vérifier cet utilisateur ?')) {
        fetch(`/admin/users/${userId}/verify`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Utilisateur vérifié avec succès', 'success');
                location.reload();
            } else {
                showNotification('Erreur lors de la vérification', 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de la vérification', 'error');
        });
    }
}

function confirmEmail(userId) {
    if (confirm('Confirmer manuellement l\'email de cet utilisateur ?')) {
        fetch(`/admin/users/${userId}/confirm-email`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Email confirmé avec succès', 'success');
                location.reload();
            } else {
                showNotification('Erreur lors de la confirmation', 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de la confirmation', 'error');
        });
    }
}

function resetPassword(userId) {
    if (confirm('Générer un nouveau mot de passe temporaire pour cet utilisateur ?')) {
        fetch(`/admin/users/${userId}/reset-password`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Nouveau mot de passe temporaire : ${data.new_password}\n\nCommuniquez-le à l'utilisateur de manière sécurisée.`);
                showNotification('Mot de passe réinitialisé avec succès', 'success');
            } else {
                showNotification('Erreur lors de la réinitialisation', 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de la réinitialisation', 'error');
        });
    }
}

function sendMessage(userId) {
    const message = prompt('Message à envoyer à l\'utilisateur :');
    if (message) {
        fetch(`/admin/users/${userId}/send-message`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ message: message })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Message envoyé avec succès', 'success');
            } else {
                showNotification('Erreur lors de l\'envoi', 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de l\'envoi', 'error');
        });
    }
}

function viewLogs(userId) {
    window.open(`/admin/users/${userId}/logs`, '_blank');
}

function saveNotes(userId) {
    const notes = document.getElementById('admin_notes').value;
    
    fetch(`/admin/users/${userId}/save-notes`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notes: notes })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Notes sauvegardées avec succès', 'success');
        } else {
            showNotification('Erreur lors de la sauvegarde', 'error');
        }
    })
    .catch(error => {
        showNotification('Erreur lors de la sauvegarde', 'error');
    });
}
</script>
{% endblock %}

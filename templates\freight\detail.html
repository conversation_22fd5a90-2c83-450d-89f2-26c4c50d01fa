{% extends "base.html" %}

{% block title %}{{ offer.title }} - SABTRANS{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-10 mx-auto">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-box me-2"></i>{{ offer.title }}
                </h1>
                <div>
                    <a href="{{ url_for('freight.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour aux offres
                    </a>
                    {% if current_user.id != offer.user_id and offer.status == 'active' %}
                        {% if offer.offer_type == 'demande' and current_user.user_type == 'transporteur' %}
                            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#proposeModal">
                                <i class="fas fa-euro-sign me-2"></i>Proposer un Prix
                            </button>
                        {% else %}
                            <button class="btn btn-success" onclick="contactOffer()">
                                <i class="fas fa-envelope me-2"></i>Contacter
                            </button>
                        {% endif %}
                    {% endif %}
                </div>
            </div>

            <div class="row">
                <!-- Informations principales -->
                <div class="col-lg-8 mb-4">
                    <!-- En-tête de l'offre -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>Informations Générales
                                </h5>
                                <div>
                                    {% if offer.is_urgent %}
                                        <span class="badge bg-danger">URGENT</span>
                                    {% endif %}
                                    {% if offer.is_private %}
                                        <span class="badge bg-warning">PRIVÉ</span>
                                    {% endif %}
                                    <span class="badge bg-{{ 'success' if offer.status == 'active' else 'secondary' }}">
                                        {% if offer.status %}
                                            {{ offer.status.title() }}
                                        {% else %}
                                            Statut non défini
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h4>{{ offer.title }}</h4>
                                    {% if offer.description %}
                                        <p class="text-muted">{{ offer.description }}</p>
                                    {% endif %}
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label text-muted">Type d'offre</label>
                                            <p>
                                                <span class="badge bg-{{ 'primary' if offer.offer_type == 'demande' else 'success' }} fs-6">
                                                    {% if offer.offer_type %}
                                                        {{ offer.offer_type.title() }}
                                                    {% else %}
                                                        Type non défini
                                                    {% endif %}
                                                </span>
                                            </p>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label text-muted">Date de collecte</label>
                                            <p>
                                                <i class="fas fa-calendar text-success me-2"></i>
                                                {% if offer.pickup_date %}
                                                    {{ offer.pickup_date.strftime('%d/%m/%Y') }}
                                                {% else %}
                                                    <span class="text-muted">Date non définie</span>
                                                {% endif %}
                                                {% if offer.flexible_dates %}
                                                    <span class="badge bg-info ms-2">Dates flexibles</span>
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    {% if offer.price %}
                                    <div class="price-display">
                                        <h2 class="text-success mb-0">{{ "%.0f"|format(offer.price) }}€</h2>
                                        <small class="text-muted">{{ offer.price_type }}</small>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Marchandise -->
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-box me-2"></i>Marchandise
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Type de marchandise</label>
                                    <p><strong>{{ offer.goods_type }}</strong></p>
                                </div>
                                {% if offer.packaging %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Conditionnement</label>
                                    <p>{{ offer.packaging }}</p>
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="row">
                                {% if offer.weight %}
                                <div class="col-md-4 mb-3">
                                    <label class="form-label text-muted">Poids</label>
                                    <p><i class="fas fa-weight-hanging text-primary me-2"></i>{{ offer.weight }} tonnes</p>
                                </div>
                                {% endif %}
                                {% if offer.volume %}
                                <div class="col-md-4 mb-3">
                                    <label class="form-label text-muted">Volume</label>
                                    <p><i class="fas fa-cube text-info me-2"></i>{{ offer.volume }} m³</p>
                                </div>
                                {% endif %}
                                {% if offer.quantity %}
                                <div class="col-md-4 mb-3">
                                    <label class="form-label text-muted">Quantité</label>
                                    <p><i class="fas fa-boxes text-warning me-2"></i>{{ offer.quantity }} unités</p>
                                </div>
                                {% endif %}
                            </div>
                            
                            {% if offer.goods_description %}
                            <div class="mb-3">
                                <label class="form-label text-muted">Description détaillée</label>
                                <p>{{ offer.goods_description }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Itinéraire -->
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-route me-2"></i>Itinéraire
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- Collecte -->
                                <div class="col-md-6 mb-4">
                                    <h6 class="text-success">
                                        <i class="fas fa-map-marker-alt me-2"></i>Lieu de Collecte
                                    </h6>
                                    <address class="mb-2">
                                        <strong>{{ offer.pickup_address }}</strong><br>
                                        {{ offer.pickup_postal_code }} {{ offer.pickup_city }}<br>
                                        {{ offer.pickup_country }}
                                    </address>
                                    {% if offer.pickup_date %}
                                    <p class="text-info">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ offer.pickup_date.strftime('%d/%m/%Y') }}
                                        {% if offer.pickup_date.time() != offer.pickup_date.time().replace(hour=0, minute=0) %}
                                            à {{ offer.pickup_date.strftime('%H:%M') }}
                                        {% endif %}
                                    </p>
                                    {% endif %}
                                </div>
                                
                                <!-- Livraison -->
                                <div class="col-md-6 mb-4">
                                    <h6 class="text-danger">
                                        <i class="fas fa-flag-checkered me-2"></i>Lieu de Livraison
                                    </h6>
                                    <address class="mb-2">
                                        <strong>{{ offer.delivery_address }}</strong><br>
                                        {{ offer.delivery_postal_code }} {{ offer.delivery_city }}<br>
                                        {{ offer.delivery_country }}
                                    </address>
                                    {% if offer.delivery_date %}
                                    <p class="text-info">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ offer.delivery_date.strftime('%d/%m/%Y') }}
                                        {% if offer.delivery_date.time() != offer.delivery_date.time().replace(hour=0, minute=0) %}
                                            à {{ offer.delivery_date.strftime('%H:%M') }}
                                        {% endif %}
                                    </p>
                                    {% endif %}
                                </div>
                            </div>
                            
                            {% if offer.distance_km %}
                            <div class="alert alert-info">
                                <i class="fas fa-road me-2"></i>
                                <strong>Distance estimée :</strong> {{ offer.distance_km }} km
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Véhicule requis -->
                    {% if offer.vehicle_type or offer.vehicle_length or offer.special_requirements %}
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-truck me-2"></i>Véhicule Requis
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {% if offer.vehicle_type %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Type de véhicule</label>
                                    <p><span class="badge bg-info">{{ offer.vehicle_type }}</span></p>
                                </div>
                                {% endif %}
                                {% if offer.vehicle_length %}
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Longueur</label>
                                    <p>{{ offer.vehicle_length }} mètres</p>
                                </div>
                                {% endif %}
                            </div>
                            
                            {% if offer.special_requirements %}
                            <div class="mb-3">
                                <label class="form-label text-muted">Exigences particulières</label>
                                <p>{{ offer.special_requirements }}</p>
                            </div>
                            {% endif %}
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-0">
                                        <i class="fas fa-shield-alt me-2 text-{{ 'success' if offer.requires_insurance else 'muted' }}"></i>
                                        Assurance {{ 'requise' if offer.requires_insurance else 'non requise' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Informations complémentaires -->
                <div class="col-lg-4">
                    <!-- Expéditeur -->
                    <div class="card mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                {% if offer.offer_type == 'demande' %}Expéditeur{% else %}Transporteur{% endif %}
                            </h5>
                        </div>
                        <div class="card-body">
                            <h6>{{ offer.user.company_name or offer.user.get_full_name() }}</h6>
                            {% if offer.user.company_name %}
                                <p class="text-muted">{{ offer.user.get_full_name() }}</p>
                            {% endif %}
                            
                            <div class="mb-3">
                                <small class="text-muted">Type de compte</small>
                                <p>
                                    <span class="badge bg-primary">
                                        {% if offer.user.user_type %}
                                            {{ offer.user.user_type.title() }}
                                        {% else %}
                                            Type non défini
                                        {% endif %}
                                    </span>
                                </p>
                            </div>
                            
                            {% if offer.user.city %}
                            <div class="mb-3">
                                <small class="text-muted">Localisation</small>
                                <p><i class="fas fa-map-marker-alt text-primary me-1"></i>{{ offer.user.city }}</p>
                            </div>
                            {% endif %}
                            
                            <div class="mb-3">
                                <small class="text-muted">Membre depuis</small>
                                <p>
                                    {% if offer.user.created_at %}
                                        {{ offer.user.created_at.strftime('%m/%Y') }}
                                    {% else %}
                                        Date inconnue
                                    {% endif %}
                                </p>
                            </div>
                            
                            {% if current_user.id != offer.user_id %}
                            <div class="d-grid">
                                <button class="btn btn-primary" onclick="contactOffer()">
                                    <i class="fas fa-envelope me-2"></i>Contacter
                                </button>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Détails de l'offre -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Détails
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted">Statut</small>
                                <p>
                                    <span class="badge bg-{{ 'success' if offer.status == 'active' else 'secondary' }}">
                                        {% if offer.status %}
                                            {{ offer.status.title() }}
                                        {% else %}
                                            Statut non défini
                                        {% endif %}
                                    </span>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <small class="text-muted">Publié le</small>
                                <p>
                                    {% if offer.created_at %}
                                        {{ offer.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                    {% else %}
                                        Date inconnue
                                    {% endif %}
                                </p>
                            </div>
                            
                            {% if offer.expires_at %}
                            <div class="mb-3">
                                <small class="text-muted">Expire le</small>
                                <p class="text-warning">{{ offer.expires_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                            </div>
                            {% endif %}

                            <div class="mb-3">
                                <small class="text-muted">Dernière modification</small>
                                <p>
                                    {% if offer.updated_at %}
                                        {{ offer.updated_at.strftime('%d/%m/%Y à %H:%M') }}
                                    {% else %}
                                        Date inconnue
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    {% if current_user.id == offer.user_id %}
                    <div class="card">
                        <div class="card-header bg-dark text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-tools me-2"></i>Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                {% if offer.status == 'active' %}
                                <a href="{{ url_for('freight.edit', offer_id=offer.id) }}" class="btn btn-warning">
                                    <i class="fas fa-edit me-2"></i>Modifier
                                </a>
                                <button class="btn btn-outline-danger" onclick="cancelOffer()">
                                    <i class="fas fa-times me-2"></i>Annuler l'offre
                                </button>
                                {% endif %}
                                
                                {% if offer.status == 'assigned' %}
                                <a href="#" class="btn btn-info">
                                    <i class="fas fa-tasks me-2"></i>Voir la mission
                                </a>
                                {% endif %}
                                
                                <button class="btn btn-outline-primary" onclick="shareOffer()">
                                    <i class="fas fa-share-alt me-2"></i>Partager
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Section Propositions (pour les demandes) -->
    {% if offer.offer_type == 'demande' and proposals %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-euro-sign me-2"></i>Propositions Reçues ({{ proposals|length }})
                        {% if current_user.id == offer.user_id %}
                            <small class="ms-2">- Classées par prix croissant</small>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% for proposal in proposals %}
                    <div class="card mb-3 {% if proposal.is_best_price() %}border-success{% endif %}">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-1">
                                            {{ proposal.transporter.get_full_name() }}
                                            {% if proposal.transporter.company_name %}
                                                <small class="text-muted">- {{ proposal.transporter.company_name }}</small>
                                            {% endif %}
                                            {% if proposal.is_best_price() %}
                                                <span class="badge bg-success ms-2">MEILLEUR PRIX</span>
                                            {% endif %}
                                        </h6>
                                        <small class="text-muted">
                                            {% if proposal.created_at %}
                                                {{ proposal.created_at.strftime('%d/%m/%Y %H:%M') }}
                                            {% else %}
                                                Date inconnue
                                            {% endif %}
                                        </small>
                                    </div>

                                    {% if proposal.message %}
                                    <p class="text-muted mb-2">{{ proposal.message }}</p>
                                    {% endif %}

                                    <!-- Détails étendus de la proposition -->
                                    <div class="row">
                                        {% if proposal.estimated_duration %}
                                        <div class="col-md-4">
                                            <small class="text-muted">Durée estimée :</small>
                                            <p class="mb-1"><i class="fas fa-clock me-1"></i>{{ proposal.estimated_duration }}h</p>
                                        </div>
                                        {% endif %}
                                        {% if proposal.vehicle_type %}
                                        <div class="col-md-4">
                                            <small class="text-muted">Type de véhicule :</small>
                                            <p class="mb-1"><i class="fas fa-truck me-1"></i>{{ proposal.vehicle_type }}</p>
                                        </div>
                                        {% endif %}
                                        {% if proposal.vehicle_capacity %}
                                        <div class="col-md-4">
                                            <small class="text-muted">Capacité :</small>
                                            <p class="mb-1"><i class="fas fa-weight me-1"></i>{{ proposal.vehicle_capacity }}t</p>
                                        </div>
                                        {% endif %}
                                    </div>

                                    {% if proposal.vehicle_details or proposal.additional_services %}
                                    <div class="row mt-2">
                                        {% if proposal.vehicle_details %}
                                        <div class="col-md-6">
                                            <small class="text-muted">Détails du véhicule :</small>
                                            <p class="mb-1">{{ proposal.vehicle_details }}</p>
                                        </div>
                                        {% endif %}
                                        {% if proposal.additional_services %}
                                        <div class="col-md-6">
                                            <small class="text-muted">Services additionnels :</small>
                                            <p class="mb-1">{{ proposal.additional_services }}</p>
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% endif %}

                                    <!-- Flexibilité et assurance -->
                                    {% if proposal.pickup_flexibility or proposal.delivery_flexibility or proposal.insurance_coverage %}
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <div class="d-flex flex-wrap gap-2">
                                                {% if proposal.pickup_flexibility %}
                                                    <span class="badge bg-info"><i class="fas fa-clock me-1"></i>Collecte flexible</span>
                                                {% endif %}
                                                {% if proposal.delivery_flexibility %}
                                                    <span class="badge bg-info"><i class="fas fa-clock me-1"></i>Livraison flexible</span>
                                                {% endif %}
                                                {% if proposal.insurance_coverage %}
                                                    <span class="badge bg-success"><i class="fas fa-shield-alt me-1"></i>Assurance {{ proposal.insurance_coverage }}€</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                    {% endif %}

                                    <!-- Expérience -->
                                    {% if proposal.similar_transports_count or proposal.references %}
                                    <div class="row mt-2">
                                        {% if proposal.similar_transports_count %}
                                        <div class="col-md-6">
                                            <small class="text-muted">Expérience similaire :</small>
                                            <p class="mb-1"><i class="fas fa-star me-1"></i>{{ proposal.similar_transports_count }} transports similaires</p>
                                        </div>
                                        {% endif %}
                                        {% if proposal.references %}
                                        <div class="col-md-6">
                                            <small class="text-muted">Références :</small>
                                            <p class="mb-1">{{ proposal.references }}</p>
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% endif %}

                                    <div class="row mt-2">
                                        <div class="col-md-4">
                                            <small class="text-muted">Prix au km :</small>
                                            <p class="mb-0">{{ "%.2f"|format(proposal.get_price_per_km()) }}€/km</p>
                                        </div>
                                        {% if offer.weight %}
                                        <div class="col-md-4">
                                            <small class="text-muted">Prix à la tonne :</small>
                                            <p class="mb-0">{{ "%.2f"|format(proposal.get_price_per_ton()) }}€/t</p>
                                        </div>
                                        {% endif %}
                                        <div class="col-md-4">
                                            <span class="badge bg-{{ 'success' if proposal.status == 'pending' else 'secondary' }}">
                                                {% if proposal.status %}
                                                    {{ proposal.status.title() }}
                                                {% else %}
                                                    Statut non défini
                                                {% endif %}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4 text-end">
                                    <div class="price-display mb-3">
                                        <h3 class="text-success mb-0">{{ "%.0f"|format(proposal.proposed_price) }}€</h3>
                                        <small class="text-muted">{{ proposal.price_type }}</small>
                                    </div>

                                    {% if current_user.id == offer.user_id and proposal.status == 'pending' %}
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-success" onclick="acceptProposal({{ proposal.id }})">
                                            <i class="fas fa-check me-2"></i>Accepter
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" onclick="rejectProposal({{ proposal.id }})">
                                            <i class="fas fa-times me-2"></i>Rejeter
                                        </button>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Modal Proposition de Prix -->
{% if current_user.id != offer.user_id and offer.offer_type == 'demande' and current_user.user_type == 'transporteur' and offer.status == 'active' %}
<div class="modal fade" id="proposeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-euro-sign me-2"></i>Proposer un Prix
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="proposeForm">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Demande :</strong> {{ offer.title }}<br>
                        <strong>Trajet :</strong> {{ offer.pickup_city }} → {{ offer.delivery_city }}
                        {% if offer.distance_km %}({{ offer.distance_km }} km){% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="proposed_price" class="form-label">Prix proposé (€) *</label>
                            <input type="number" class="form-control" id="proposed_price" name="proposed_price"
                                   min="1" step="0.01" required>
                            <div class="form-text">Prix total pour le transport</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="price_type" class="form-label">Type de prix</label>
                            <select class="form-select" id="price_type" name="price_type">
                                <option value="fixe">Prix fixe</option>
                                <option value="negociable">Négociable</option>
                                <option value="au_km">Au kilomètre</option>
                            </select>
                        </div>
                    </div>

                    <!-- Détails du véhicule -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-truck me-2"></i>Détails du Véhicule</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="vehicle_type" class="form-label">Type de véhicule</label>
                                    <select class="form-select" id="vehicle_type" name="vehicle_type">
                                        <option value="">Sélectionner...</option>
                                        <option value="Fourgon">Fourgon</option>
                                        <option value="Camion">Camion</option>
                                        <option value="Semi-remorque">Semi-remorque</option>
                                        <option value="Camion bâché">Camion bâché</option>
                                        <option value="Camion frigorifique">Camion frigorifique</option>
                                        <option value="Plateau">Plateau</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="vehicle_registration" class="form-label">Immatriculation</label>
                                    <input type="text" class="form-control" id="vehicle_registration" name="vehicle_registration"
                                           placeholder="Ex: AB-123-CD">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="vehicle_capacity" class="form-label">Capacité (tonnes)</label>
                                    <input type="number" class="form-control" id="vehicle_capacity" name="vehicle_capacity"
                                           min="0.1" step="0.1" placeholder="Ex: 3.5">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="vehicle_volume" class="form-label">Volume (m³)</label>
                                    <input type="number" class="form-control" id="vehicle_volume" name="vehicle_volume"
                                           min="0.1" step="0.1" placeholder="Ex: 20">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="estimated_duration" class="form-label">Durée estimée (heures)</label>
                                    <input type="number" class="form-control" id="estimated_duration" name="estimated_duration"
                                           min="1" max="168">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="vehicle_details" class="form-label">Détails supplémentaires</label>
                                <input type="text" class="form-control" id="vehicle_details" name="vehicle_details"
                                       placeholder="Ex: Équipé d'un hayon, GPS, etc.">
                            </div>
                        </div>
                    </div>

                    <!-- Services et flexibilité -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Services et Flexibilité</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="insurance_coverage" class="form-label">Couverture assurance (€)</label>
                                    <input type="number" class="form-control" id="insurance_coverage" name="insurance_coverage"
                                           min="0" step="1000" placeholder="Ex: 50000">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="similar_transports_count" class="form-label">Transports similaires effectués</label>
                                    <input type="number" class="form-control" id="similar_transports_count" name="similar_transports_count"
                                           min="0" placeholder="Ex: 25">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="pickup_flexibility" name="pickup_flexibility">
                                        <label class="form-check-label" for="pickup_flexibility">
                                            Flexibilité sur l'heure de collecte
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="delivery_flexibility" name="delivery_flexibility">
                                        <label class="form-check-label" for="delivery_flexibility">
                                            Flexibilité sur l'heure de livraison
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="additional_services" class="form-label">Services additionnels</label>
                                <input type="text" class="form-control" id="additional_services" name="additional_services"
                                       placeholder="Ex: Déchargement, emballage, stockage temporaire...">
                            </div>
                        </div>
                    </div>

                    <!-- Message et références -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="message" class="form-label">Message personnalisé</label>
                            <textarea class="form-control" id="message" name="message" rows="4"
                                      placeholder="Décrivez vos services, votre expérience, vos atouts..."></textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="references" class="form-label">Références clients</label>
                            <textarea class="form-control" id="references" name="references" rows="4"
                                      placeholder="Noms d'entreprises ou contacts de référence..."></textarea>
                        </div>
                    </div>

                    {% if offer.distance_km %}
                    <div class="alert alert-secondary">
                        <strong>Calculs automatiques :</strong>
                        <div id="price-calculations" class="mt-2"></div>
                    </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-paper-plane me-2"></i>Envoyer la Proposition
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function contactOffer() {
    // Ici on pourrait implémenter un système de messagerie
    alert('Fonctionnalité de contact en cours de développement.\n\nVous pouvez contacter directement :\n{{ offer.user.get_full_name() }}\n{% if offer.user.phone %}Tél: {{ offer.user.phone }}{% endif %}');
}

function cancelOffer() {
    if (confirm('Êtes-vous sûr de vouloir annuler cette offre ? Cette action est irréversible.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/freight/{{ offer.id }}/cancel';
        document.body.appendChild(form);
        form.submit();
    }
}

function shareOffer() {
    const url = window.location.href;
    const title = '{{ offer.title }}';

    if (navigator.share) {
        navigator.share({
            title: title,
            text: 'Offre de fret SABTRANS',
            url: url
        });
    } else {
        // Fallback : copier l'URL
        navigator.clipboard.writeText(url).then(() => {
            showNotification('Lien copié dans le presse-papiers', 'success');
        });
    }
}

// Gestion des propositions de prix
function acceptProposal(proposalId) {
    if (confirm('Êtes-vous sûr de vouloir accepter cette proposition ? Cela créera automatiquement une mission et rejettera les autres propositions.')) {
        fetch(`/freight/proposal/${proposalId}/accept`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de l\'acceptation', 'error');
        });
    }
}

function rejectProposal(proposalId) {
    if (confirm('Êtes-vous sûr de vouloir rejeter cette proposition ?')) {
        fetch(`/freight/proposal/${proposalId}/reject`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'info');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors du rejet', 'error');
        });
    }
}

// Gestion du formulaire de proposition
document.addEventListener('DOMContentLoaded', function() {
    const proposeForm = document.getElementById('proposeForm');
    if (proposeForm) {
        proposeForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('/freight/{{ offer.id }}/propose', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('proposeModal')).hide();
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                showNotification('Erreur lors de l\'envoi', 'error');
            });
        });

        // Calculs automatiques du prix
        const priceInput = document.getElementById('proposed_price');
        const calculationsDiv = document.getElementById('price-calculations');

        if (priceInput && calculationsDiv) {
            priceInput.addEventListener('input', function() {
                const price = parseFloat(this.value) || 0;
                const distance = {{ offer.distance_km or 0 }};
                const weight = {{ offer.weight or 0 }};

                let calculations = '';

                if (distance > 0) {
                    const pricePerKm = (price / distance).toFixed(2);
                    calculations += `<strong>Prix au km :</strong> ${pricePerKm}€/km<br>`;
                }

                if (weight > 0) {
                    const pricePerTon = (price / weight).toFixed(2);
                    calculations += `<strong>Prix à la tonne :</strong> ${pricePerTon}€/t<br>`;
                }

                if (distance > 0 && weight > 0) {
                    const efficiency = ((price / distance) / weight).toFixed(2);
                    calculations += `<strong>Efficacité :</strong> ${efficiency}€/km/t`;
                }

                calculationsDiv.innerHTML = calculations;
            });
        }
    }
});
</script>
{% endblock %}

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from models.document import Document
from models.mission import Mission
from app import db
import os
import uuid
from datetime import datetime
from PIL import Image
import mimetypes

documents_bp = Blueprint('documents', __name__)

ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB

def allowed_file(filename):
    """Vérifie si l'extension du fichier est autorisée"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def generate_unique_filename(original_filename):
    """Génère un nom de fichier unique"""
    ext = os.path.splitext(original_filename)[1]
    unique_name = str(uuid.uuid4()) + ext
    return unique_name

@documents_bp.route('/')
@login_required
def index():
    """Liste des documents de l'utilisateur"""
    page = request.args.get('page', 1, type=int)
    document_type = request.args.get('type', '')
    search_query = request.args.get('search', '')
    
    # Construction de la requête
    query = Document.query.filter_by(user_id=current_user.id)
    
    if document_type:
        query = query.filter_by(document_type=document_type)
    
    if search_query:
        query = query.filter(
            db.or_(
                Document.name.contains(search_query),
                Document.description.contains(search_query),
                Document.tags.contains(search_query)
            )
        )
    
    # Tri par date de création (plus récent en premier)
    documents = query.order_by(db.desc(Document.created_at)).paginate(
        page=page, per_page=12, error_out=False
    )
    
    # Types de documents pour le filtre
    document_types = [
        ('cmr', 'CMR'),
        ('pod', 'Preuve de livraison'),
        ('facture', 'Facture'),
        ('bon_livraison', 'Bon de livraison'),
        ('photo', 'Photo'),
        ('assurance', 'Assurance'),
        ('permis', 'Permis'),
        ('kbis', 'Kbis'),
        ('protocole', 'Protocole'),
        ('autre', 'Autre')
    ]
    
    return render_template('documents/index.html',
                         documents=documents,
                         document_types=document_types,
                         current_type=document_type,
                         search_query=search_query)

@documents_bp.route('/upload', methods=['GET', 'POST'])
@login_required
def upload():
    """Upload d'un nouveau document"""
    if request.method == 'POST':
        # Vérifier qu'un fichier a été sélectionné
        if 'file' not in request.files:
            flash('Aucun fichier sélectionné', 'error')
            return redirect(request.url)
        
        file = request.files['file']
        if file.filename == '':
            flash('Aucun fichier sélectionné', 'error')
            return redirect(request.url)
        
        # Récupération des autres données du formulaire
        name = request.form.get('name', '').strip()
        document_type = request.form.get('document_type', '')
        description = request.form.get('description', '').strip()
        tags = request.form.get('tags', '').strip()
        mission_id = request.form.get('mission_id', type=int)
        is_public = bool(request.form.get('is_public'))
        
        # Validation
        if not name:
            name = file.filename
        
        if not document_type:
            flash('Veuillez sélectionner un type de document', 'error')
            return redirect(request.url)
        
        if file and allowed_file(file.filename):
            # Vérifier la taille du fichier
            file.seek(0, os.SEEK_END)
            file_size = file.tell()
            file.seek(0)
            
            if file_size > MAX_FILE_SIZE:
                flash('Le fichier est trop volumineux (maximum 16MB)', 'error')
                return redirect(request.url)
            
            # Générer un nom de fichier unique
            original_filename = secure_filename(file.filename)
            unique_filename = generate_unique_filename(original_filename)
            
            # Créer le dossier de destination
            upload_folder = current_app.config['UPLOAD_FOLDER']
            user_folder = os.path.join(upload_folder, str(current_user.id))
            os.makedirs(user_folder, exist_ok=True)
            
            # Chemin complet du fichier
            file_path = os.path.join(user_folder, unique_filename)
            
            try:
                # Sauvegarder le fichier
                file.save(file_path)
                
                # Déterminer le type MIME
                mime_type, _ = mimetypes.guess_type(original_filename)
                
                # Créer l'enregistrement en base
                document = Document(
                    user_id=current_user.id,
                    mission_id=mission_id if mission_id else None,
                    name=name,
                    original_filename=original_filename,
                    filename=unique_filename,
                    file_path=file_path,
                    file_size=file_size,
                    mime_type=mime_type,
                    document_type=document_type,
                    description=description,
                    is_public=is_public
                )
                
                # Ajouter les tags
                if tags:
                    document.add_tags(tags)
                
                db.session.add(document)
                db.session.commit()
                
                flash('Document uploadé avec succès!', 'success')
                return redirect(url_for('documents.detail', document_id=document.id))
                
            except Exception as e:
                # Supprimer le fichier en cas d'erreur
                if os.path.exists(file_path):
                    os.remove(file_path)
                db.session.rollback()
                flash('Erreur lors de l\'upload du document', 'error')
        else:
            flash('Type de fichier non autorisé', 'error')
    
    # Récupérer les missions de l'utilisateur pour le formulaire
    missions = Mission.query.filter(
        db.or_(
            Mission.transporter_id == current_user.id,
            Mission.shipper_id == current_user.id,
            Mission.driver_id == current_user.id
        )
    ).order_by(db.desc(Mission.created_at)).all()
    
    return render_template('documents/upload.html', missions=missions)

@documents_bp.route('/<int:document_id>')
@login_required
def detail(document_id):
    """Détail d'un document"""
    document = Document.query.get_or_404(document_id)
    
    # Vérifier les droits d'accès
    if document.user_id != current_user.id and not document.is_public:
        # Vérifier si l'utilisateur a accès via une mission
        if document.mission_id:
            mission = Mission.query.get(document.mission_id)
            if mission and current_user.id not in [mission.transporter_id, mission.shipper_id, mission.driver_id]:
                flash('Accès non autorisé à ce document', 'error')
                return redirect(url_for('documents.index'))
        else:
            flash('Accès non autorisé à ce document', 'error')
            return redirect(url_for('documents.index'))
    
    return render_template('documents/detail.html', document=document)

@documents_bp.route('/<int:document_id>/download')
@login_required
def download(document_id):
    """Télécharger un document"""
    document = Document.query.get_or_404(document_id)
    
    # Vérifier les droits d'accès (même logique que detail)
    if document.user_id != current_user.id and not document.is_public:
        if document.mission_id:
            mission = Mission.query.get(document.mission_id)
            if mission and current_user.id not in [mission.transporter_id, mission.shipper_id, mission.driver_id]:
                flash('Accès non autorisé à ce document', 'error')
                return redirect(url_for('documents.index'))
        else:
            flash('Accès non autorisé à ce document', 'error')
            return redirect(url_for('documents.index'))
    
    # Vérifier que le fichier existe
    if not os.path.exists(document.file_path):
        flash('Fichier introuvable', 'error')
        return redirect(url_for('documents.detail', document_id=document_id))
    
    return send_file(
        document.file_path,
        as_attachment=True,
        download_name=document.original_filename,
        mimetype=document.mime_type
    )

@documents_bp.route('/<int:document_id>/preview')
@login_required
def preview(document_id):
    """Prévisualisation d'un document (pour les images)"""
    document = Document.query.get_or_404(document_id)
    
    # Vérifier les droits d'accès
    if document.user_id != current_user.id and not document.is_public:
        if document.mission_id:
            mission = Mission.query.get(document.mission_id)
            if mission and current_user.id not in [mission.transporter_id, mission.shipper_id, mission.driver_id]:
                return "Accès non autorisé", 403
        else:
            return "Accès non autorisé", 403
    
    # Vérifier que c'est une image
    if not document.is_image():
        return "Prévisualisation non disponible pour ce type de fichier", 400
    
    # Vérifier que le fichier existe
    if not os.path.exists(document.file_path):
        return "Fichier introuvable", 404
    
    return send_file(document.file_path, mimetype=document.mime_type)

@documents_bp.route('/<int:document_id>/sign', methods=['POST'])
@login_required
def sign_document(document_id):
    """Signer électroniquement un document"""
    document = Document.query.get_or_404(document_id)
    
    # Vérifier les droits d'accès
    if document.user_id != current_user.id:
        if document.mission_id:
            mission = Mission.query.get(document.mission_id)
            if mission and current_user.id not in [mission.transporter_id, mission.shipper_id, mission.driver_id]:
                return jsonify({'error': 'Accès non autorisé'}), 403
        else:
            return jsonify({'error': 'Accès non autorisé'}), 403
    
    # Récupérer les données de signature
    signature_data = request.json.get('signature_data', '')
    
    if not signature_data:
        return jsonify({'error': 'Données de signature manquantes'}), 400
    
    try:
        # Signer le document
        document.sign_document(
            signed_by=current_user.get_full_name(),
            signature_data=signature_data
        )
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Document signé avec succès',
            'signed_at': document.signed_at.isoformat(),
            'signed_by': document.signed_by
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Erreur lors de la signature'}), 500

@documents_bp.route('/<int:document_id>/delete', methods=['POST'])
@login_required
def delete(document_id):
    """Supprimer un document"""
    document = Document.query.get_or_404(document_id)
    
    # Vérifier que l'utilisateur est le propriétaire
    if document.user_id != current_user.id:
        flash('Vous ne pouvez supprimer que vos propres documents', 'error')
        return redirect(url_for('documents.detail', document_id=document_id))
    
    try:
        # Supprimer le fichier physique
        if os.path.exists(document.file_path):
            os.remove(document.file_path)
        
        # Supprimer l'enregistrement en base
        db.session.delete(document)
        db.session.commit()
        
        flash('Document supprimé avec succès', 'success')
        return redirect(url_for('documents.index'))
        
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de la suppression du document', 'error')
        return redirect(url_for('documents.detail', document_id=document_id))

@documents_bp.route('/mission/<int:mission_id>')
@login_required
def mission_documents(mission_id):
    """Documents liés à une mission"""
    mission = Mission.query.get_or_404(mission_id)
    
    # Vérifier l'accès à la mission
    if current_user.id not in [mission.transporter_id, mission.shipper_id, mission.driver_id]:
        flash('Accès non autorisé à cette mission', 'error')
        return redirect(url_for('dashboard.index'))
    
    documents = Document.query.filter_by(mission_id=mission_id).order_by(
        db.desc(Document.created_at)
    ).all()
    
    return render_template('documents/mission_documents.html',
                         mission=mission,
                         documents=documents)

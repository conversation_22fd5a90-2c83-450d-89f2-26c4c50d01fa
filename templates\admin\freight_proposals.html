{% extends "base.html" %}

{% block title %}Gestion des Propositions de Fret - SABTRANS{% endblock %}

{# Initialisation sécurisée des variables #}
{% set filters = filters or {} %}
{% set total_proposals = total_proposals or 0 %}
{% set pending_proposals = pending_proposals or 0 %}
{% set accepted_proposals = accepted_proposals or 0 %}
{% set rejected_proposals = rejected_proposals or 0 %}
{% set top_demands = top_demands or [] %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary">
                        <i class="fas fa-euro-sign me-2"></i>Gestion des Propositions de Fret
                    </h1>
                    <p class="text-muted">Suivi des propositions de prix des transporteurs</p>
                </div>
                <div>
                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Total Propositions</h6>
                            <h2 class="stat-number">{{ total_proposals }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-euro-sign fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card warning h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">En Attente</h6>
                            <h2 class="stat-number text-warning">{{ pending_proposals }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card success h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Acceptées</h6>
                            <h2 class="stat-number text-success">{{ accepted_proposals }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card danger h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Rejetées</h6>
                            <h2 class="stat-number text-danger">{{ rejected_proposals }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times fa-2x text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Demandes les plus populaires -->
    {% if top_demands %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-fire me-2"></i>Demandes les Plus Populaires
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for demand in top_demands %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title">{{ demand.title }}</h6>
                                    <p class="text-muted small mb-2">{{ demand.pickup_city }} → {{ demand.delivery_city }}</p>
                                    <div class="d-flex justify-content-between">
                                        <span class="badge bg-info">{{ demand.proposal_count }} propositions</span>
                                        <span class="text-success fw-bold">{{ "%.0f"|format(demand.best_price) }}€</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Filtres et liste des propositions -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>Liste des Propositions
            </h5>
        </div>
        <div class="card-body">
            <!-- Filtres -->
            <form method="GET" class="mb-4">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="status" class="form-label">Statut</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">Tous les statuts</option>
                            <option value="pending" {% if filters.get('status') == 'pending' %}selected{% endif %}>En attente</option>
                            <option value="accepted" {% if filters.get('status') == 'accepted' %}selected{% endif %}>Acceptées</option>
                            <option value="rejected" {% if filters.get('status') == 'rejected' %}selected{% endif %}>Rejetées</option>
                            <option value="withdrawn" {% if filters.get('status') == 'withdrawn' %}selected{% endif %}>Retirées</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="sort_by" class="form-label">Trier par</label>
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="created_at" {% if filters.get('sort_by') == 'created_at' %}selected{% endif %}>Date de création</option>
                            <option value="proposed_price" {% if filters.get('sort_by') == 'proposed_price' %}selected{% endif %}>Prix proposé</option>
                            <option value="freight_title" {% if filters.get('sort_by') == 'freight_title' %}selected{% endif %}>Titre de la demande</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="order" class="form-label">Ordre</label>
                        <select class="form-select" id="order" name="order">
                            <option value="desc" {% if filters.get('order') == 'desc' %}selected{% endif %}>Décroissant</option>
                            <option value="asc" {% if filters.get('order') == 'asc' %}selected{% endif %}>Croissant</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-2"></i>Filtrer
                        </button>
                    </div>
                </div>
            </form>

            {% if proposals and proposals.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Demande</th>
                                <th>Transporteur</th>
                                <th>Prix Proposé</th>
                                <th>Détails</th>
                                <th>Statut</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for proposal in proposals.items %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ proposal.freight_offer.title }}</strong><br>
                                        <small class="text-muted">{{ proposal.freight_offer.pickup_city }} → {{ proposal.freight_offer.delivery_city }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ proposal.transporter.get_full_name() }}</strong><br>
                                        {% if proposal.transporter.company_name %}
                                            <small class="text-muted">{{ proposal.transporter.company_name }}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <h5 class="mb-0 text-success">{{ "%.0f"|format(proposal.proposed_price) }}€</h5>
                                        <small class="text-muted">{{ proposal.price_type }}</small>
                                        {% if proposal.is_best_price() %}
                                            <br><span class="badge bg-warning">MEILLEUR PRIX</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if proposal.message %}
                                        <small class="text-muted">{{ proposal.message[:50] }}...</small><br>
                                    {% endif %}
                                    {% if proposal.estimated_duration %}
                                        <small><i class="fas fa-clock me-1"></i>{{ proposal.estimated_duration }}h</small><br>
                                    {% endif %}
                                    {% if proposal.vehicle_details %}
                                        <small><i class="fas fa-truck me-1"></i>{{ proposal.vehicle_details[:30] }}...</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'warning' if proposal.status == 'pending' else 'success' if proposal.status == 'accepted' else 'danger' if proposal.status == 'rejected' else 'secondary' }}">
                                        {% if proposal.status %}
                                            {{ proposal.status.title() }}
                                        {% else %}
                                            Statut non défini
                                        {% endif %}
                                    </span>
                                </td>
                                <td>
                                    <small>
                                        {% if proposal.created_at %}
                                            {{ proposal.created_at.strftime('%d/%m/%Y %H:%M') }}
                                        {% else %}
                                            Date inconnue
                                        {% endif %}
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('freight.detail', offer_id=proposal.freight_offer.id) }}" 
                                           class="btn btn-outline-primary" title="Voir la demande">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if proposals.pages > 1 %}
                <nav aria-label="Navigation des propositions" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if proposals.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.freight_proposals', page=proposals.prev_num, status=filters.get('status', ''), sort_by=filters.get('sort_by', ''), order=filters.get('order', '')) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in proposals.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != proposals.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.freight_proposals', page=page_num, status=filters.get('status', ''), sort_by=filters.get('sort_by', ''), order=filters.get('order', '')) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if proposals.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.freight_proposals', page=proposals.next_num, status=filters.get('status', ''), sort_by=filters.get('sort_by', ''), order=filters.get('order', '')) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <!-- Aucune proposition -->
                <div class="text-center py-5">
                    <i class="fas fa-euro-sign fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Aucune proposition trouvée</h4>
                    <p class="text-muted">
                        {% if filters.get('status') %}
                            Aucune proposition ne correspond à vos critères de recherche.
                        {% else %}
                            Aucune proposition n'a encore été soumise.
                        {% endif %}
                    </p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.dashboard-card {
    border-left: 4px solid;
}

.dashboard-card.success {
    border-left-color: #28a745;
}

.dashboard-card.warning {
    border-left-color: #ffc107;
}

.dashboard-card.danger {
    border-left-color: #dc3545;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}
</style>
{% endblock %}

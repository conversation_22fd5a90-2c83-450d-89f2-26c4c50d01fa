from datetime import datetime
from database import db

class Mission(db.Model):
    __tablename__ = 'missions'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    
    # Références
    freight_offer_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>('freight_offers.id'), nullable=False)
    transporter_id = db.Column(db.In<PERSON>ger, db.<PERSON>('users.id'), nullable=False)
    shipper_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    driver_id = db.Column(db.Integer, db.<PERSON>Key('users.id'), nullable=True)
    
    # Informations de base
    mission_number = db.Column(db.String(50), unique=True, nullable=False)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    
    # Statut de la mission
    status = db.Column(db.<PERSON>(
        'pending',      # En attente d'acceptation
        'accepted',     # Acceptée par le transporteur
        'assigned',     # Chauffeur assigné
        'pickup_ready', # Prêt pour collecte
        'in_transit',   # En cours de transport
        'delivered',    # Livré
        'completed',    # Mission terminée
        'cancelled'     # Annulée
    ), default='pending')
    
    # Informations de collecte
    pickup_scheduled_at = db.Column(db.DateTime)
    pickup_actual_at = db.Column(db.DateTime)
    pickup_notes = db.Column(db.Text)
    pickup_signature = db.Column(db.Text)  # signature base64
    pickup_photos = db.Column(db.Text)     # URLs des photos séparées par des virgules
    
    # Informations de livraison
    delivery_scheduled_at = db.Column(db.DateTime)
    delivery_actual_at = db.Column(db.DateTime)
    delivery_notes = db.Column(db.Text)
    delivery_signature = db.Column(db.Text)  # signature base64
    delivery_photos = db.Column(db.Text)     # URLs des photos séparées par des virgules
    
    # Géolocalisation
    current_lat = db.Column(db.Float)
    current_lng = db.Column(db.Float)
    last_position_update = db.Column(db.DateTime)
    
    # Informations financières
    agreed_price = db.Column(db.Float)
    currency = db.Column(db.String(3), default='EUR')
    payment_status = db.Column(db.Enum('pending', 'paid', 'overdue'), default='pending')
    invoice_generated = db.Column(db.Boolean, default=False)
    invoice_number = db.Column(db.String(50))
    
    # Évaluation
    shipper_rating = db.Column(db.Integer)  # Note de 1 à 5
    transporter_rating = db.Column(db.Integer)  # Note de 1 à 5
    shipper_comment = db.Column(db.Text)
    transporter_comment = db.Column(db.Text)
    
    # Incidents et problèmes
    has_incidents = db.Column(db.Boolean, default=False)
    incident_description = db.Column(db.Text)
    
    # Dates
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = db.Column(db.DateTime)
    
    # Relations
    documents = db.relationship('Document', backref='mission', lazy='dynamic')
    
    def generate_mission_number(self):
        """Génère un numéro de mission unique"""
        import random
        import string
        
        # Format: SAB-YYYYMMDD-XXXX
        date_str = datetime.utcnow().strftime('%Y%m%d')
        random_str = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
        self.mission_number = f"SAB-{date_str}-{random_str}"
        
        # Vérifier l'unicité
        while Mission.query.filter_by(mission_number=self.mission_number).first():
            random_str = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
            self.mission_number = f"SAB-{date_str}-{random_str}"
    
    def update_status(self, new_status, notes=None):
        """Met à jour le statut de la mission"""
        old_status = self.status
        self.status = new_status
        self.updated_at = datetime.utcnow()
        
        # Actions spécifiques selon le statut
        if new_status == 'completed':
            self.completed_at = datetime.utcnow()
        elif new_status == 'pickup_ready':
            self.pickup_actual_at = datetime.utcnow()
        elif new_status == 'delivered':
            self.delivery_actual_at = datetime.utcnow()
        
        # Log du changement de statut (à implémenter si nécessaire)
        return old_status, new_status
    
    def update_position(self, lat, lng):
        """Met à jour la position actuelle"""
        self.current_lat = lat
        self.current_lng = lng
        self.last_position_update = datetime.utcnow()
    
    def calculate_duration(self):
        """Calcule la durée de la mission"""
        if self.pickup_actual_at and self.delivery_actual_at:
            duration = self.delivery_actual_at - self.pickup_actual_at
            return duration.total_seconds() / 3600  # en heures
        return None
    
    def is_overdue(self):
        """Vérifie si la mission est en retard"""
        if self.delivery_scheduled_at and self.status not in ['delivered', 'completed', 'cancelled']:
            return datetime.utcnow() > self.delivery_scheduled_at
        return False
    
    def get_progress_percentage(self):
        """Calcule le pourcentage d'avancement de la mission"""
        status_progress = {
            'pending': 0,
            'accepted': 10,
            'assigned': 20,
            'pickup_ready': 40,
            'in_transit': 70,
            'delivered': 90,
            'completed': 100,
            'cancelled': 0
        }
        return status_progress.get(self.status, 0)
    
    def get_status_label(self):
        """Retourne le libellé du statut en français"""
        status_labels = {
            'pending': 'En attente',
            'accepted': 'Acceptée',
            'assigned': 'Chauffeur assigné',
            'pickup_ready': 'Prêt pour collecte',
            'in_transit': 'En transit',
            'delivered': 'Livré',
            'completed': 'Terminée',
            'cancelled': 'Annulée'
        }
        return status_labels.get(self.status, self.status)
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire pour l'API"""
        return {
            'id': self.id,
            'mission_number': self.mission_number,
            'title': self.title,
            'status': self.status,
            'status_label': self.get_status_label(),
            'progress_percentage': self.get_progress_percentage(),
            'pickup_scheduled_at': self.pickup_scheduled_at.isoformat() if self.pickup_scheduled_at else None,
            'delivery_scheduled_at': self.delivery_scheduled_at.isoformat() if self.delivery_scheduled_at else None,
            'agreed_price': self.agreed_price,
            'currency': self.currency,
            'payment_status': self.payment_status,
            'current_lat': self.current_lat,
            'current_lng': self.current_lng,
            'is_overdue': self.is_overdue(),
            'duration_hours': self.calculate_duration(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def get_status_label(self):
        """Retourne le libellé du statut en français"""
        status_labels = {
            'pending': 'En attente',
            'accepted': 'Acceptée',
            'assigned': 'Assignée',
            'pickup_ready': 'Prêt pour collecte',
            'in_transit': 'En transit',
            'delivered': 'Livrée',
            'completed': 'Terminée',
            'cancelled': 'Annulée'
        }
        return status_labels.get(self.status, self.status)

    def get_progress_percentage(self):
        """Retourne le pourcentage de progression de la mission"""
        progress_map = {
            'pending': 0,
            'accepted': 10,
            'assigned': 20,
            'pickup_ready': 30,
            'in_transit': 60,
            'delivered': 90,
            'completed': 100,
            'cancelled': 0
        }
        return progress_map.get(self.status, 0)

    def __repr__(self):
        return f'<Mission {self.mission_number}>'

{% extends "base.html" %}

{% block title %}Notifications - SABTRANS{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary">
                        <i class="fas fa-bell me-2"></i>Notifications
                    </h1>
                    <p class="text-muted">Suivez l'activité de vos demandes et propositions</p>
                </div>
                <div>
                    <a href="{{ url_for('freight.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications -->
    <div class="row">
        <div class="col-12">
            {% if notifications.items %}
                <div class="card">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-inbox me-2"></i>Vos Notifications
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            {% for notification in notifications.items %}
                            <div class="list-group-item {% if not notification.is_read %}list-group-item-light{% endif %}">
                                <div class="d-flex w-100 justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="{{ notification.get_icon() }} me-2"></i>
                                            <h6 class="mb-0">{{ notification.title }}</h6>
                                            {% if not notification.is_read %}
                                                <span class="badge bg-primary ms-2">Nouveau</span>
                                            {% endif %}
                                        </div>
                                        <p class="mb-2">{{ notification.message }}</p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ notification.created_at.strftime('%d/%m/%Y à %H:%M') }}
                                        </small>
                                    </div>
                                    <div class="ms-3">
                                        {% if notification.get_url() != '/dashboard' %}
                                            <a href="{{ notification.get_url() }}" class="btn btn-sm btn-outline-primary me-2">
                                                <i class="fas fa-eye me-1"></i>Voir
                                            </a>
                                        {% endif %}
                                        <button class="btn btn-sm btn-outline-secondary archive-notification" 
                                                data-notification-id="{{ notification.id }}">
                                            <i class="fas fa-archive me-1"></i>Archiver
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                {% if notifications.pages > 1 %}
                <nav aria-label="Navigation des notifications" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if notifications.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('freight.notifications', page=notifications.prev_num) }}">
                                    <i class="fas fa-chevron-left"></i> Précédent
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in notifications.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != notifications.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('freight.notifications', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if notifications.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('freight.notifications', page=notifications.next_num) }}">
                                    Suivant <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <!-- Aucune notification -->
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune notification</h5>
                        <p class="text-muted">Vous n'avez aucune notification pour le moment.</p>
                        <a href="{{ url_for('freight.index') }}" class="btn btn-primary">
                            <i class="fas fa-exchange-alt me-2"></i>Voir les Demandes
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gestion de l'archivage des notifications
    document.querySelectorAll('.archive-notification').forEach(button => {
        button.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;
            
            fetch(`/freight/notifications/${notificationId}/archive`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token() }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Supprimer la notification de l'affichage
                    this.closest('.list-group-item').remove();
                    
                    // Vérifier s'il reste des notifications
                    const remainingNotifications = document.querySelectorAll('.list-group-item').length;
                    if (remainingNotifications === 0) {
                        location.reload(); // Recharger pour afficher le message "aucune notification"
                    }
                } else {
                    alert('Erreur lors de l\'archivage de la notification');
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                alert('Erreur lors de l\'archivage de la notification');
            });
        });
    });
});
</script>
{% endblock %}

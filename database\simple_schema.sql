-- SABTRANS Database Schema - Version simplifiée
-- Base de données MySQL pour la plateforme SABTRANS

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(120) NOT NULL UNIQUE,
    username VA<PERSON>HA<PERSON>(80) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    
    -- Informations personnelles
    first_name VA<PERSON>HAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    
    -- Type d'utilisateur
    user_type ENUM('transporteur', 'expediteur', 'chauffeur', 'admin') NOT NULL,
    
    -- Informations entreprise
    company_name VA<PERSON>HAR(100),
    siret VARCHAR(20),
    address TEXT,
    city VARCHAR(50),
    postal_code VARCHAR(10),
    country VARCHAR(50) DEFAULT 'France',
    
    -- Statut et validation
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    email_confirmed BOOLEAN DEFAULT FALSE,
    
    -- Dates
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_user_type (user_type),
    INDEX idx_created_at (created_at)
);

-- Table des offres de fret
CREATE TABLE IF NOT EXISTS freight_offers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    
    -- Informations de base
    title VARCHAR(200) NOT NULL,
    description TEXT,
    offer_type ENUM('demande', 'offre') NOT NULL,
    
    -- Marchandise
    goods_type VARCHAR(100) NOT NULL,
    goods_description TEXT,
    weight DECIMAL(10,2),
    volume DECIMAL(10,2),
    quantity INT,
    packaging VARCHAR(50),
    
    -- Lieux de collecte
    pickup_address TEXT NOT NULL,
    pickup_city VARCHAR(100) NOT NULL,
    pickup_postal_code VARCHAR(10),
    pickup_country VARCHAR(50) DEFAULT 'France',
    pickup_lat DECIMAL(10,8),
    pickup_lng DECIMAL(11,8),
    
    -- Lieux de livraison
    delivery_address TEXT NOT NULL,
    delivery_city VARCHAR(100) NOT NULL,
    delivery_postal_code VARCHAR(10),
    delivery_country VARCHAR(50) DEFAULT 'France',
    delivery_lat DECIMAL(10,8),
    delivery_lng DECIMAL(11,8),
    
    -- Dates
    pickup_date DATETIME NOT NULL,
    delivery_date DATETIME,
    flexible_dates BOOLEAN DEFAULT FALSE,
    
    -- Véhicule requis
    vehicle_type VARCHAR(50),
    vehicle_length DECIMAL(5,2),
    special_requirements TEXT,
    
    -- Prix
    price DECIMAL(10,2),
    price_type ENUM('fixe', 'negociable', 'au_km') DEFAULT 'negociable',
    currency VARCHAR(3) DEFAULT 'EUR',
    
    -- Distance estimée
    distance_km DECIMAL(8,2),
    
    -- Statut
    status ENUM('active', 'assigned', 'completed', 'cancelled') DEFAULT 'active',
    
    -- Options
    is_urgent BOOLEAN DEFAULT FALSE,
    is_private BOOLEAN DEFAULT FALSE,
    requires_insurance BOOLEAN DEFAULT TRUE,
    
    -- Dates système
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_offer_type (offer_type),
    INDEX idx_status (status),
    INDEX idx_pickup_date (pickup_date),
    INDEX idx_pickup_city (pickup_city),
    INDEX idx_delivery_city (delivery_city),
    INDEX idx_created_at (created_at)
);

-- Table des missions
CREATE TABLE IF NOT EXISTS missions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    freight_offer_id INT NOT NULL,
    transporter_id INT NOT NULL,
    shipper_id INT NOT NULL,
    driver_id INT,
    
    -- Informations de base
    mission_number VARCHAR(50) NOT NULL UNIQUE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    
    -- Statut de la mission
    status ENUM('pending', 'accepted', 'assigned', 'pickup_ready', 'in_transit', 'delivered', 'completed', 'cancelled') DEFAULT 'pending',
    
    -- Informations de collecte
    pickup_scheduled_at DATETIME,
    pickup_actual_at DATETIME,
    pickup_notes TEXT,
    pickup_signature TEXT,
    pickup_photos TEXT,
    
    -- Informations de livraison
    delivery_scheduled_at DATETIME,
    delivery_actual_at DATETIME,
    delivery_notes TEXT,
    delivery_signature TEXT,
    delivery_photos TEXT,
    
    -- Géolocalisation
    current_lat DECIMAL(10,8),
    current_lng DECIMAL(11,8),
    last_position_update TIMESTAMP NULL,
    
    -- Informations financières
    agreed_price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'EUR',
    payment_status ENUM('pending', 'paid', 'overdue') DEFAULT 'pending',
    invoice_generated BOOLEAN DEFAULT FALSE,
    invoice_number VARCHAR(50),
    
    -- Évaluation
    shipper_rating INT CHECK (shipper_rating >= 1 AND shipper_rating <= 5),
    transporter_rating INT CHECK (transporter_rating >= 1 AND transporter_rating <= 5),
    shipper_comment TEXT,
    transporter_comment TEXT,
    
    -- Incidents et problèmes
    has_incidents BOOLEAN DEFAULT FALSE,
    incident_description TEXT,
    
    -- Dates
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    
    FOREIGN KEY (freight_offer_id) REFERENCES freight_offers(id) ON DELETE CASCADE,
    FOREIGN KEY (transporter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (shipper_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (driver_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_mission_number (mission_number),
    INDEX idx_freight_offer_id (freight_offer_id),
    INDEX idx_transporter_id (transporter_id),
    INDEX idx_shipper_id (shipper_id),
    INDEX idx_driver_id (driver_id),
    INDEX idx_status (status),
    INDEX idx_pickup_scheduled (pickup_scheduled_at),
    INDEX idx_delivery_scheduled (delivery_scheduled_at),
    INDEX idx_created_at (created_at)
);

-- Table des documents
CREATE TABLE IF NOT EXISTS documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    mission_id INT,
    
    -- Informations du document
    name VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    
    -- Type de document
    document_type ENUM('cmr', 'pod', 'facture', 'bon_livraison', 'photo', 'assurance', 'permis', 'kbis', 'protocole', 'autre') NOT NULL,
    
    -- Description et tags
    description TEXT,
    tags VARCHAR(500),
    
    -- Statut
    status ENUM('draft', 'pending', 'approved', 'rejected') DEFAULT 'draft',
    
    -- Signature électronique
    is_signed BOOLEAN DEFAULT FALSE,
    signed_by VARCHAR(100),
    signed_at TIMESTAMP NULL,
    signature_data TEXT,
    
    -- Métadonnées
    is_public BOOLEAN DEFAULT FALSE,
    is_archived BOOLEAN DEFAULT FALSE,
    
    -- Dates
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (mission_id) REFERENCES missions(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_mission_id (mission_id),
    INDEX idx_document_type (document_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Table des notifications
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    related_type ENUM('mission', 'freight_offer', 'document', 'user') NULL,
    related_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

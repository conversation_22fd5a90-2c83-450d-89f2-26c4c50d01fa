from datetime import datetime
import os
from database import db

class Document(db.Model):
    __tablename__ = 'documents'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    
    # Référence utilisateur
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('users.id'), nullable=False)
    
    # Référence mission (optionnel)
    mission_id = db.Column(db.Integer, db.ForeignKey('missions.id'), nullable=True)
    
    # Informations du document
    name = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    filename = db.Column(db.String(255), nullable=False)  # nom du fichier sur le serveur
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)  # taille en bytes
    mime_type = db.Column(db.String(100))
    
    # Type de document
    document_type = db.Column(db.Enum(
        'cmr', 'pod', 'facture', 'bon_livraison', 'photo', 'assurance', 
        'permis', 'kbis', 'protocole', 'autre'
    ), nullable=False)
    
    # Description et tags
    description = db.Column(db.Text)
    tags = db.Column(db.String(500))  # tags séparés par des virgules
    
    # Statut
    status = db.Column(db.Enum('draft', 'pending', 'approved', 'rejected'), default='draft')
    
    # Signature électronique
    is_signed = db.Column(db.Boolean, default=False)
    signed_by = db.Column(db.String(100))
    signed_at = db.Column(db.DateTime)
    signature_data = db.Column(db.Text)  # données de signature en base64
    
    # Métadonnées
    is_public = db.Column(db.Boolean, default=False)
    is_archived = db.Column(db.Boolean, default=False)
    
    # Dates
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def get_file_extension(self):
        """Retourne l'extension du fichier"""
        return os.path.splitext(self.original_filename)[1].lower()
    
    def is_image(self):
        """Vérifie si le document est une image"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        return self.get_file_extension() in image_extensions
    
    def is_pdf(self):
        """Vérifie si le document est un PDF"""
        return self.get_file_extension() == '.pdf'
    
    def get_size_formatted(self):
        """Retourne la taille du fichier formatée"""
        if not self.file_size:
            return "Taille inconnue"
        
        size = self.file_size
        units = ['B', 'KB', 'MB', 'GB']
        unit_index = 0
        
        while size >= 1024 and unit_index < len(units) - 1:
            size /= 1024
            unit_index += 1
        
        return f"{size:.1f} {units[unit_index]}"
    
    def get_download_url(self):
        """Retourne l'URL de téléchargement du document"""
        from flask import url_for
        return url_for('documents.download', document_id=self.id)
    
    def get_preview_url(self):
        """Retourne l'URL de prévisualisation du document"""
        from flask import url_for
        if self.is_image():
            return url_for('documents.preview', document_id=self.id)
        return None
    
    def sign_document(self, signed_by, signature_data=None):
        """Signe le document électroniquement"""
        self.is_signed = True
        self.signed_by = signed_by
        self.signed_at = datetime.utcnow()
        if signature_data:
            self.signature_data = signature_data
        self.status = 'approved'
    
    def add_tags(self, tag_list):
        """Ajoute des tags au document"""
        if isinstance(tag_list, list):
            existing_tags = self.get_tags()
            all_tags = list(set(existing_tags + tag_list))
            self.tags = ','.join(all_tags)
        elif isinstance(tag_list, str):
            self.tags = tag_list
    
    def get_tags(self):
        """Retourne la liste des tags"""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        return []
    
    def to_dict(self):
        """Convertit l'objet en dictionnaire pour l'API"""
        return {
            'id': self.id,
            'name': self.name,
            'original_filename': self.original_filename,
            'document_type': self.document_type,
            'file_size': self.file_size,
            'file_size_formatted': self.get_size_formatted(),
            'mime_type': self.mime_type,
            'description': self.description,
            'tags': self.get_tags(),
            'status': self.status,
            'is_signed': self.is_signed,
            'signed_by': self.signed_by,
            'signed_at': self.signed_at.isoformat() if self.signed_at else None,
            'is_image': self.is_image(),
            'is_pdf': self.is_pdf(),
            'download_url': self.get_download_url(),
            'preview_url': self.get_preview_url(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    


    def __repr__(self):
        return f'<Document {self.name}>'

{% extends "base.html" %}

{% block title %}Dashboard Chauffeur - SABTRANS{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 text-primary">
                <i class="fas fa-user-tie me-2"></i>Dashboard Chauffeur
            </h1>
            <p class="text-muted">Bienvenue {{ current_user.get_full_name() }}</p>
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Missions Totales</h6>
                            <h2 class="stat-number">{{ total_missions }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clipboard-list fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card warning h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Missions Actives</h6>
                            <h2 class="stat-number text-warning">{{ active_missions }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shipping-fast fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card success h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Missions Terminées</h6>
                            <h2 class="stat-number text-success">{{ completed_missions }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card info h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Statut</h6>
                            <h2 class="stat-number text-info">
                                {% if current_mission %}
                                    <i class="fas fa-truck-moving"></i>
                                {% else %}
                                    <i class="fas fa-parking"></i>
                                {% endif %}
                            </h2>
                        </div>
                        <div class="align-self-center">
                            {% if current_mission %}
                                <span class="badge bg-success fs-6">En mission</span>
                            {% else %}
                                <span class="badge bg-secondary fs-6">Disponible</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mission actuelle -->
    {% if current_mission %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-truck-moving me-2"></i>Mission Actuelle
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h4>{{ current_mission.mission_number }}</h4>
                            <p class="mb-2"><strong>{{ current_mission.title }}</strong></p>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <h6 class="text-success">
                                        <i class="fas fa-map-marker-alt me-2"></i>Collecte
                                    </h6>
                                    <p class="mb-1">{{ current_mission.freight_offer.pickup_address }}</p>
                                    <p class="text-muted">{{ current_mission.freight_offer.pickup_city }}</p>
                                    {% if current_mission.pickup_scheduled_at %}
                                        <p class="text-info">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ current_mission.pickup_scheduled_at.strftime('%d/%m/%Y à %H:%M') }}
                                        </p>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-danger">
                                        <i class="fas fa-flag-checkered me-2"></i>Livraison
                                    </h6>
                                    <p class="mb-1">{{ current_mission.freight_offer.delivery_address }}</p>
                                    <p class="text-muted">{{ current_mission.freight_offer.delivery_city }}</p>
                                    {% if current_mission.delivery_scheduled_at %}
                                        <p class="text-info">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ current_mission.delivery_scheduled_at.strftime('%d/%m/%Y à %H:%M') }}
                                        </p>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="progress mb-3">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ current_mission.get_progress_percentage() }}%">
                                    {{ current_mission.get_progress_percentage() }}%
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 text-center">
                            <div class="mb-3">
                                <span class="badge bg-{{ 'success' if current_mission.status == 'in_transit' else 'warning' }} fs-6">
                                    {{ current_mission.get_status_label() }}
                                </span>
                            </div>
                            
                            <div class="d-grid gap-2">
                                {% if current_mission.status == 'assigned' %}
                                    <button class="btn btn-success" onclick="updateMissionStatus('{{ current_mission.id }}', 'pickup_ready')">
                                        <i class="fas fa-play me-2"></i>Commencer la collecte
                                    </button>
                                {% elif current_mission.status == 'pickup_ready' %}
                                    <button class="btn btn-primary" onclick="updateMissionStatus('{{ current_mission.id }}', 'in_transit')">
                                        <i class="fas fa-truck me-2"></i>En route
                                    </button>
                                {% elif current_mission.status == 'in_transit' %}
                                    <button class="btn btn-warning" onclick="updateMissionStatus('{{ current_mission.id }}', 'delivered')">
                                        <i class="fas fa-check me-2"></i>Marquer comme livré
                                    </button>
                                {% endif %}
                                
                                <a href="{{ url_for('documents.mission_documents', mission_id=current_mission.id) }}" 
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-file-alt me-2"></i>Documents
                                </a>
                                
                                <button class="btn btn-outline-info" onclick="shareLocation()">
                                    <i class="fas fa-map-marker-alt me-2"></i>Partager position
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row">
        <!-- Missions du jour -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>Missions du Jour
                    </h5>
                </div>
                <div class="card-body">
                    {% if today_missions %}
                        <div class="list-group list-group-flush">
                            {% for mission in today_missions %}
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">{{ mission.mission_number }}</div>
                                    <small class="text-muted">
                                        {{ mission.freight_offer.pickup_city }} → {{ mission.freight_offer.delivery_city }}
                                    </small>
                                    {% if mission.pickup_scheduled_at %}
                                        <br><small class="text-info">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ mission.pickup_scheduled_at.strftime('%H:%M') }}
                                        </small>
                                    {% endif %}
                                </div>
                                <span class="badge bg-{{ 'success' if mission.status == 'completed' else 'primary' }}">
                                    {{ mission.get_status_label() }}
                                </span>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-calendar-day fa-2x text-muted mb-2"></i>
                            <p class="text-muted">Aucune mission prévue aujourd'hui</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Missions de la semaine -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-week me-2"></i>Missions de la Semaine
                    </h5>
                </div>
                <div class="card-body">
                    {% if week_missions %}
                        <div class="list-group list-group-flush">
                            {% for mission in week_missions %}
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">{{ mission.mission_number }}</div>
                                    <small class="text-muted">
                                        {{ mission.freight_offer.pickup_city }} → {{ mission.freight_offer.delivery_city }}
                                    </small>
                                    {% if mission.pickup_scheduled_at %}
                                        <br><small class="text-info">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ mission.pickup_scheduled_at.strftime('%d/%m à %H:%M') }}
                                        </small>
                                    {% endif %}
                                </div>
                                <span class="badge bg-{{ 'success' if mission.status == 'completed' else 'primary' }}">
                                    {{ mission.get_status_label() }}
                                </span>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-calendar-week fa-2x text-muted mb-2"></i>
                            <p class="text-muted">Aucune mission prévue cette semaine</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Historique des missions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Historique des Missions
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_missions %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Mission</th>
                                        <th>Itinéraire</th>
                                        <th>Date</th>
                                        <th>Statut</th>
                                        <th>Transporteur</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for mission in recent_missions %}
                                    <tr>
                                        <td>
                                            <strong>{{ mission.mission_number }}</strong><br>
                                            <small class="text-muted">{{ mission.title[:30] }}...</small>
                                        </td>
                                        <td>
                                            <small>
                                                {{ mission.freight_offer.pickup_city }} → 
                                                {{ mission.freight_offer.delivery_city }}
                                            </small>
                                        </td>
                                        <td>{{ mission.created_at.strftime('%d/%m/%Y') }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if mission.status == 'completed' else 'secondary' }}">
                                                {{ mission.get_status_label() }}
                                            </span>
                                        </td>
                                        <td>{{ mission.transporter.company_name or mission.transporter.get_full_name() }}</td>
                                        <td>
                                            <a href="{{ url_for('documents.mission_documents', mission_id=mission.id) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-file-alt"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune mission dans l'historique</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateMissionStatus(missionId, newStatus) {
    if (confirm('Confirmer le changement de statut ?')) {
        // Ici on pourrait implémenter la mise à jour du statut
        alert('Mise à jour du statut en cours de développement');
    }
}

function shareLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            
            // Ici on pourrait envoyer la position au serveur
            alert(`Position partagée: ${lat}, ${lng}`);
        }, function(error) {
            alert('Impossible d\'obtenir votre position');
        });
    } else {
        alert('Géolocalisation non supportée');
    }
}

// Actualisation automatique des données
setInterval(function() {
    console.log('Actualisation des données du dashboard chauffeur...');
}, 30000); // Toutes les 30 secondes
</script>
{% endblock %}

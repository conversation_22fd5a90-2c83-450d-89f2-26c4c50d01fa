{% extends "base.html" %}

{% block title %}{{ document.name }} - SABTRANS{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-10 mx-auto">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-file-alt me-2"></i>{{ document.name }}
                </h1>
                <div>
                    <a href="{{ url_for('documents.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour
                    </a>
                    <a href="{{ url_for('documents.download', document_id=document.id) }}" class="btn btn-success">
                        <i class="fas fa-download me-2"></i>Télécharger
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Informations du document -->
                <div class="col-lg-8 mb-4">
                    <!-- Prévisualisation -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-eye me-2"></i>Prévisualisation
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            {% if document.is_image() %}
                                <img src="{{ document.get_preview_url() }}" class="img-fluid rounded" 
                                     alt="{{ document.name }}" style="max-height: 400px;">
                            {% elif document.is_pdf() %}
                                <div class="pdf-preview">
                                    <i class="fas fa-file-pdf fa-5x text-danger mb-3"></i>
                                    <p class="text-muted">Prévisualisation PDF non disponible</p>
                                    <a href="{{ document.get_download_url() }}" class="btn btn-outline-primary">
                                        <i class="fas fa-external-link-alt me-2"></i>Ouvrir le PDF
                                    </a>
                                </div>
                            {% else %}
                                <div class="file-preview">
                                    <i class="fas fa-file fa-5x text-secondary mb-3"></i>
                                    <p class="text-muted">Prévisualisation non disponible pour ce type de fichier</p>
                                    <a href="{{ document.get_download_url() }}" class="btn btn-outline-primary">
                                        <i class="fas fa-download me-2"></i>Télécharger pour voir
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Description -->
                    {% if document.description %}
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-align-left me-2"></i>Description
                            </h5>
                        </div>
                        <div class="card-body">
                            <p>{{ document.description }}</p>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Signature électronique -->
                    {% if document.is_signed %}
                    <div class="card mb-4 border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-signature me-2"></i>Signature Électronique
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Signé par :</strong> {{ document.signed_by }}</p>
                                    <p><strong>Date de signature :</strong> {{ document.signed_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                                </div>
                                <div class="col-md-6 text-end">
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-check-circle me-1"></i>Document Signé
                                    </span>
                                </div>
                            </div>
                            {% if document.signature_data %}
                            <div class="mt-3">
                                <h6>Signature :</h6>
                                <img src="data:image/png;base64,{{ document.signature_data }}" 
                                     class="img-fluid border rounded" style="max-height: 100px;">
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% else %}
                    <!-- Bouton de signature -->
                    <div class="card mb-4 border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-pen me-2"></i>Signature Électronique
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">Ce document n'a pas encore été signé électroniquement.</p>
                            <button class="btn btn-warning" onclick="openSignatureModal()">
                                <i class="fas fa-signature me-2"></i>Signer ce document
                            </button>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Métadonnées -->
                <div class="col-lg-4">
                    <!-- Informations générales -->
                    <div class="card mb-4">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>Informations
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label text-muted">Type de document</label>
                                <p><span class="badge bg-primary">{{ document.document_type.upper() }}</span></p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Nom du fichier</label>
                                <p class="small">{{ document.original_filename }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Taille</label>
                                <p>{{ document.get_size_formatted() }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Type MIME</label>
                                <p class="small">{{ document.mime_type or 'Non défini' }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Statut</label>
                                <p>
                                    <span class="badge bg-{{ 'success' if document.status == 'approved' else 'secondary' }}">
                                        {% if document.status %}
                                            {{ document.status.title() }}
                                        {% else %}
                                            Statut non défini
                                        {% endif %}
                                    </span>
                                </p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Visibilité</label>
                                <p>
                                    {% if document.is_public %}
                                        <span class="badge bg-info">Public</span>
                                    {% else %}
                                        <span class="badge bg-secondary">Privé</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Tags -->
                    {% if document.get_tags() %}
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-tags me-2"></i>Tags
                            </h5>
                        </div>
                        <div class="card-body">
                            {% for tag in document.get_tags() %}
                                <span class="badge bg-light text-dark me-1 mb-1">{{ tag }}</span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Mission associée -->
                    {% if document.mission %}
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-link me-2"></i>Mission Associée
                            </h5>
                        </div>
                        <div class="card-body">
                            <h6>{{ document.mission.mission_number }}</h6>
                            <p class="text-muted small">{{ document.mission.title }}</p>
                            <a href="#" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-eye me-1"></i>Voir la mission
                            </a>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Dates -->
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar me-2"></i>Historique
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label text-muted">Créé le</label>
                                <p>{{ document.created_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Modifié le</label>
                                <p>{{ document.updated_at.strftime('%d/%m/%Y à %H:%M') }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label text-muted">Propriétaire</label>
                                <p>{{ document.user.get_full_name() }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="card">
                        <div class="card-header bg-dark text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-tools me-2"></i>Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ document.get_download_url() }}" class="btn btn-success">
                                    <i class="fas fa-download me-2"></i>Télécharger
                                </a>
                                
                                {% if not document.is_signed %}
                                <button class="btn btn-warning" onclick="openSignatureModal()">
                                    <i class="fas fa-signature me-2"></i>Signer
                                </button>
                                {% endif %}
                                
                                <button class="btn btn-info" onclick="shareDocument()">
                                    <i class="fas fa-share-alt me-2"></i>Partager
                                </button>
                                
                                {% if document.user_id == current_user.id %}
                                <button class="btn btn-outline-danger" onclick="deleteDocument()">
                                    <i class="fas fa-trash me-2"></i>Supprimer
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de signature -->
<div class="modal fade" id="signatureModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-signature me-2"></i>Signature Électronique
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Signez ce document en dessinant votre signature ci-dessous :</p>
                <canvas id="signatureCanvas" width="600" height="200" class="border rounded w-100"></canvas>
                <div class="mt-3">
                    <button type="button" class="btn btn-outline-secondary" onclick="clearSignature()">
                        <i class="fas fa-eraser me-2"></i>Effacer
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" onclick="saveSignature()">
                    <i class="fas fa-save me-2"></i>Signer le document
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let canvas, ctx, isDrawing = false;

function openSignatureModal() {
    const modal = new bootstrap.Modal(document.getElementById('signatureModal'));
    modal.show();
    
    // Initialiser le canvas après l'ouverture du modal
    setTimeout(initSignatureCanvas, 100);
}

function initSignatureCanvas() {
    canvas = document.getElementById('signatureCanvas');
    ctx = canvas.getContext('2d');
    
    // Configuration du canvas
    ctx.strokeStyle = '#000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    
    // Événements de dessin
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);
    
    // Support tactile
    canvas.addEventListener('touchstart', handleTouch);
    canvas.addEventListener('touchmove', handleTouch);
    canvas.addEventListener('touchend', stopDrawing);
}

function startDrawing(e) {
    isDrawing = true;
    const rect = canvas.getBoundingClientRect();
    ctx.beginPath();
    ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
}

function draw(e) {
    if (!isDrawing) return;
    const rect = canvas.getBoundingClientRect();
    ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
    ctx.stroke();
}

function stopDrawing() {
    isDrawing = false;
}

function handleTouch(e) {
    e.preventDefault();
    const touch = e.touches[0];
    const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' : 
                                     e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
        clientX: touch.clientX,
        clientY: touch.clientY
    });
    canvas.dispatchEvent(mouseEvent);
}

function clearSignature() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
}

function saveSignature() {
    const signatureData = canvas.toDataURL('image/png');
    
    // Envoyer la signature au serveur
    fetch(`/documents/{{ document.id }}/sign`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            signature_data: signatureData.split(',')[1] // Enlever le préfixe data:image/png;base64,
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Document signé avec succès', 'success');
            location.reload();
        } else {
            showNotification('Erreur lors de la signature', 'error');
        }
    })
    .catch(error => {
        showNotification('Erreur lors de la signature', 'error');
    });
    
    // Fermer le modal
    bootstrap.Modal.getInstance(document.getElementById('signatureModal')).hide();
}

function shareDocument() {
    const url = window.location.href;
    if (navigator.share) {
        navigator.share({
            title: '{{ document.name }}',
            text: 'Document SABTRANS',
            url: url
        });
    } else {
        // Fallback : copier l'URL
        navigator.clipboard.writeText(url).then(() => {
            showNotification('Lien copié dans le presse-papiers', 'success');
        });
    }
}

function deleteDocument() {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce document ? Cette action est irréversible.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/documents/{{ document.id }}/delete';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}

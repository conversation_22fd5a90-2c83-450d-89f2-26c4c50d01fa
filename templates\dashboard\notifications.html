{% extends "base.html" %}

{% block title %}Notifications - SABTRANS{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-10 mx-auto">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 text-primary">
                    <i class="fas fa-bell me-2"></i>Notifications
                </h1>
                <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour au dashboard
                </a>
            </div>

            <!-- Notifications -->
            {% if notifications %}
                <div class="row">
                    {% for notification in notifications %}
                    <div class="col-12 mb-3">
                        <div class="alert alert-{{ notification.type }} alert-dismissible fade show" role="alert">
                            <div class="d-flex align-items-start">
                                <div class="me-3">
                                    {% if notification.type == 'info' %}
                                        <i class="fas fa-info-circle fa-lg"></i>
                                    {% elif notification.type == 'success' %}
                                        <i class="fas fa-check-circle fa-lg"></i>
                                    {% elif notification.type == 'warning' %}
                                        <i class="fas fa-exclamation-triangle fa-lg"></i>
                                    {% elif notification.type == 'error' %}
                                        <i class="fas fa-times-circle fa-lg"></i>
                                    {% endif %}
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="alert-heading mb-1">{{ notification.title }}</h6>
                                    <p class="mb-2">{{ notification.message }}</p>
                                    {% if notification.url %}
                                        <a href="{{ notification.url }}" class="btn btn-sm btn-outline-{{ notification.type }}">
                                            <i class="fas fa-external-link-alt me-1"></i>Voir détails
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <!-- Aucune notification -->
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Aucune notification</h4>
                    <p class="text-muted">
                        Vous n'avez aucune notification pour le moment.
                    </p>
                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>Retour au dashboard
                    </a>
                </div>
            {% endif %}

            <!-- Paramètres de notifications -->
            <div class="card mt-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>Paramètres de Notifications
                    </h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Notifications par Email</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_new_offers" checked>
                                    <label class="form-check-label" for="email_new_offers">
                                        Nouvelles offres disponibles
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_mission_updates" checked>
                                    <label class="form-check-label" for="email_mission_updates">
                                        Mises à jour des missions
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_documents" checked>
                                    <label class="form-check-label" for="email_documents">
                                        Nouveaux documents
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>Notifications Push</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="push_urgent" checked>
                                    <label class="form-check-label" for="push_urgent">
                                        Offres urgentes
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="push_messages" checked>
                                    <label class="form-check-label" for="push_messages">
                                        Nouveaux messages
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="push_reminders">
                                    <label class="form-check-label" for="push_reminders">
                                        Rappels de missions
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Sauvegarder les préférences
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Gestion des préférences de notifications
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Récupérer les préférences
        const preferences = {
            email_new_offers: document.getElementById('email_new_offers').checked,
            email_mission_updates: document.getElementById('email_mission_updates').checked,
            email_documents: document.getElementById('email_documents').checked,
            push_urgent: document.getElementById('push_urgent').checked,
            push_messages: document.getElementById('push_messages').checked,
            push_reminders: document.getElementById('push_reminders').checked
        };
        
        // Sauvegarder en localStorage pour l'instant
        localStorage.setItem('notification_preferences', JSON.stringify(preferences));
        
        showNotification('Préférences de notifications sauvegardées', 'success');
    });
    
    // Charger les préférences sauvegardées
    const savedPreferences = localStorage.getItem('notification_preferences');
    if (savedPreferences) {
        const preferences = JSON.parse(savedPreferences);
        
        Object.keys(preferences).forEach(key => {
            const checkbox = document.getElementById(key);
            if (checkbox) {
                checkbox.checked = preferences[key];
            }
        });
    }
});
</script>
{% endblock %}
